@import "tailwindcss";
@import "primeicons/primeicons.css";


/* Custom Stripe-inspired global styles */
body {
  background: linear-gradient(135deg, #feecf9 0%, #fdf2f8 25%, #f3f0ff 50%, #fce7f3 75%, #feecf9 100%) fixed;
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
  font-family: 'Poppins', sans-serif !important;
  color: #1e293b;
  line-height: 1.6;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  font-variation-settings: normal;
  min-height: 100vh;
}

/* Subtle gradient animation */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Smooth transitions for all interactive elements */
* {
  transition: all 0.2s ease-in-out;
}

/* Remove ugly dialog shadows and borders globally */
.p-dialog {
  box-shadow: 0 20px 60px rgba(85, 33, 190, 0.15) !important;
  border: none !important;
}

.p-dialog .p-dialog-content {
  border: none !important;
}

.p-dialog .p-dialog-header {
  border: none !important;
}

.p-dialog .p-dialog-footer {
  border: none !important;
}

/* Gradient overlay for depth */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 20% 80%, rgba(85, 33, 190, 0.03) 0%, transparent 50%),
  radial-gradient(circle at 80% 20%, rgba(224, 54, 175, 0.03) 0%, transparent 50%),
  radial-gradient(circle at 40% 40%, rgba(254, 236, 249, 0.8) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

/* Custom scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Focus ring improvements */
*:focus {
  outline: none;
}

/* Custom shadow utilities */
.shadow-stripe {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.shadow-stripe-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Gradient utilities */
.bg-gradient-primary {
  background: linear-gradient(135deg, #5521be 0%, #4a1ca8 100%);
}

.bg-gradient-secondary {
  background: linear-gradient(135deg, #e036af 0%, #c026d3 100%);
}

.bg-gradient-brand {
  background: linear-gradient(135deg, #5521be 0%, #e036af 100%);
}

.bg-gradient-soft {
  background: linear-gradient(135deg, rgba(85, 33, 190, 0.05) 0%, rgba(224, 54, 175, 0.05) 100%);
}

.bg-gradient-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(254, 236, 249, 0.3) 100%);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Gradient text */
.text-gradient-brand {
  background: linear-gradient(135deg, #5521be 0%, #e036af 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Enhanced button styling for Stripe-like appearance */
.p-button {
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  letter-spacing: -0.01em !important;
  cursor: pointer !important;
  user-select: none !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  white-space: nowrap !important;
  text-decoration: none !important;
  position: relative !important;
  overflow: hidden !important;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11' !important;
  min-height: 2rem !important;
}

.p-button:not(.p-button-text):not(.p-button-outlined) {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
}

.p-button:active {
  transform: translateY(0) !important;
}

.p-button:focus-visible {
  outline: none !important;
  box-shadow: 0 0 0 3px rgba(85, 33, 190, 0.12) !important;
}

/* Remove default PrimeNG button shadows that might conflict */
.p-button.p-component {
  box-shadow: none !important;
}

.p-button.p-component:enabled:hover {
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1) !important;
}

/* Confirm Dialog Specific Styling */
.p-confirmdialog .p-dialog {
  background: #ffffff !important;
  border: 1px solid rgba(85, 33, 190, 0.2) !important;
  border-radius: 1rem !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
}

.p-confirmdialog .p-dialog-header {
  background: linear-gradient(135deg, #5521be 0%, #e036af 100%) !important;
  border: none !important;
  color: #ffffff !important;
  padding: 1.5rem 2rem !important;
  border-radius: 1rem 1rem 0 0 !important;
}

.p-confirmdialog .p-dialog-title {
  font-weight: 600 !important;
  font-size: 1.125rem !important;
  line-height: 1.4 !important;
  color: #ffffff !important;
}

.p-confirmdialog .p-dialog-header-close {
  background: rgba(255, 255, 255, 0.2) !important;
  color: #ffffff !important;
  border: none !important;
  border-radius: 0.5rem !important;
  width: 2rem !important;
  height: 2rem !important;
  transition: all 0.2s ease !important;
}

.p-confirmdialog .p-dialog-header-close:hover {
  background: rgba(255, 255, 255, 0.3) !important;
  color: #ffffff !important;
}

.p-confirmdialog .p-dialog-content {
  background: #ffffff !important;
  color: #475569 !important;
  padding: 2rem !important;
  font-size: 0.95rem !important;
  line-height: 1.6 !important;
}

.p-confirmdialog .p-confirmdialog-icon {
  font-size: 1.5rem !important;
  color: #5521be !important;
  margin-right: 1rem !important;
}

.p-confirmdialog .p-confirmdialog-message {
  margin-left: 0 !important;
}

.p-confirmdialog .p-dialog-footer {
  background: #ffffff !important;
  border: none !important;
  border-top: 1px solid #e2e8f0 !important;
  padding: 1.5rem 2rem !important;
  gap: 0.75rem !important;
  border-radius: 0 0 1rem 1rem !important;
}

.p-confirmdialog .p-confirm-dialog-accept {
  background: #5521be !important;
  border: 1px solid #5521be !important;
  color: #ffffff !important;
  border-radius: 0.5rem !important;
  padding: 0.75rem 1.5rem !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
}

.p-confirmdialog .p-confirm-dialog-accept:hover {
  background: #4a1ca8 !important;
  border-color: #4a1ca8 !important;
}

.p-confirmdialog .p-confirm-dialog-reject {
  background: transparent !important;
  border: 1px solid #cbd5e1 !important;
  color: #64748b !important;
  border-radius: 0.5rem !important;
  padding: 0.75rem 1.5rem !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
}

.p-confirmdialog .p-confirm-dialog-reject:hover {
  background: rgba(71, 85, 105, 0.08) !important;
  border-color: #94a3b8 !important;
  color: #475569 !important;
}

/* Confirm Dialog Mask */
.p-confirmdialog-mask {
  background: rgba(0, 0, 0, 0.6) !important;
  backdrop-filter: blur(8px) !important;
}

/* Clickable Table Rows */
.clickable-row {
  cursor: pointer !important;
  transition: background-color 0.2s ease !important;
}

.clickable-row:hover {
  background-color: rgba(85, 33, 190, 0.05) !important;
}

.clickable-row:active {
  background-color: rgba(85, 33, 190, 0.1) !important;
}

/* Context Menu styling is now handled by Chainmatic theme */
