<!-- Sidebar (outside main container) -->
<chm-sidebar #sidebar *ngIf="showSidebar" (stateChange)="onSidebarStateChange($event)"></chm-sidebar>

<div class="app-container">
  <!-- Mobile Header -->
  <header *ngIf="showSidebar" class="mobile-header">
    <button (click)="toggleMobileSidebar()" class="mobile-menu-btn">
      <i class="pi pi-bars"></i>
    </button>
    <img alt="Chainmatic" class="mobile-logo" src="images/chainmatic-portal.png">
  </header>

  <main [class.no-sidebar]="!showSidebar" [ngStyle]="showSidebar ? mainContentClass : {}" class="main-content">
    <div class="content-wrapper">
      <router-outlet></router-outlet>
    </div>
  </main>
</div>
