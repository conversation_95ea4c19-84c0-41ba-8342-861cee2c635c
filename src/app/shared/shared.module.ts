import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

// PrimeNG Modules
import { ButtonModule } from 'primeng/button';
import { MenuModule } from 'primeng/menu';
import { BadgeModule } from 'primeng/badge';
import { TooltipModule } from 'primeng/tooltip';
import { CardModule } from 'primeng/card';
import { PanelModule } from 'primeng/panel';
import { InputTextModule } from 'primeng/inputtext';
import { DropdownModule } from 'primeng/dropdown';
import { ToastModule } from 'primeng/toast';

// Components
import { SidebarComponent } from './components/sidebar/sidebar.component';

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    RouterModule,
    ButtonModule,
    MenuModule,
    BadgeModule,
    TooltipModule,
    CardModule,
    PanelModule,
    InputTextModule,
    DropdownModule,
    ToastModule,
    SidebarComponent // Import standalone component
  ],
  exports: [
    // PrimeNG Modules
    ButtonModule,
    MenuModule,
    BadgeModule,
    TooltipModule,
    CardModule,
    PanelModule,
    InputTextModule,
    DropdownModule,
    ToastModule,
    
    // Components
    SidebarComponent
  ]
})
export class SharedModule { }
