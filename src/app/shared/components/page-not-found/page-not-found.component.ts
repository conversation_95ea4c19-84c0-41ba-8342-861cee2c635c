import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonModule } from 'primeng/button';
import { Router } from '@angular/router';

@Component({
  selector: 'app-page-not-found',
  standalone: true,
  imports: [CommonModule, ButtonModule],
  template: `
    <div class="not-found-container">
      <div class="not-found-content">
        <div class="icon-section">
          <i class="pi pi-exclamation-triangle not-found-icon"></i>
        </div>
        
        <h1 class="not-found-title">🚧 Page Under Construction</h1>
        
        <p class="not-found-message">
          {{ message || 'This page is not yet available or is currently being developed.' }}
        </p>
        
        <div class="not-found-details" *ngIf="pageId">
          <p class="page-info">
            <strong>Page ID:</strong> {{ pageId }}
          </p>
          <p class="convention-info">
            <strong>Expected:</strong> <code>pages/{{ pageId }}/{{ pageId }}.component.ts</code>
          </p>
        </div>
        
        <div class="action-buttons">
          <p-button 
            label="Go to Ad Winners"
            icon="pi pi-home"
            (onClick)="goToHome()"
            styleClass="home-btn">
          </p-button>
          
          <p-button 
            label="Go Back"
            icon="pi pi-arrow-left"
            [outlined]="true"
            (onClick)="goBack()"
            styleClass="back-btn">
          </p-button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .not-found-container {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 60vh;
      padding: 2rem;
    }

    .not-found-content {
      text-align: center;
      max-width: 500px;
    }

    .icon-section {
      margin-bottom: 1.5rem;
    }

    .not-found-icon {
      font-size: 4rem;
      color: #f59e0b;
    }

    .not-found-title {
      font-size: 1.875rem;
      font-weight: 700;
      color: #1e293b;
      margin: 0 0 1rem 0;
    }

    .not-found-message {
      font-size: 1.125rem;
      color: #64748b;
      margin: 0 0 2rem 0;
      line-height: 1.6;
    }

    .not-found-details {
      background: #f8fafc;
      border: 1px solid #e2e8f0;
      border-radius: 0.5rem;
      padding: 1rem;
      margin-bottom: 2rem;
      text-align: left;
    }

    .page-info,
    .convention-info {
      margin: 0.5rem 0;
      font-size: 0.875rem;
      color: #374151;
    }

    .convention-info code {
      background: #e5e7eb;
      padding: 0.125rem 0.25rem;
      border-radius: 0.25rem;
      font-family: 'Monaco', 'Menlo', monospace;
      font-size: 0.75rem;
    }

    .action-buttons {
      display: flex;
      gap: 1rem;
      justify-content: center;
      flex-wrap: wrap;
    }

    .home-btn {
      background: linear-gradient(135deg, #5521be 0%, #e036af 100%) !important;
      border: none !important;
    }

    .back-btn {
      border-color: #5521be !important;
      color: #5521be !important;
    }

    .back-btn:hover {
      background: #5521be !important;
      color: white !important;
    }

    @media (max-width: 640px) {
      .action-buttons {
        flex-direction: column;
        align-items: center;
      }
      
      .action-buttons :host ::ng-deep .p-button {
        width: 200px;
      }
    }
  `]
})
export class PageNotFoundComponent {
  @Input() pageId?: string;
  @Input() message?: string;

  constructor(private router: Router) {}

  goToHome(): void {
    this.router.navigate(['/ad-winners']);
  }

  goBack(): void {
    window.history.back();
  }
}
