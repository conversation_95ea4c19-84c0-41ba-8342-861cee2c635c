<!-- Mobile Overlay -->
<div (click)="closeMobileSidebar()" *ngIf="isMobileOpen" class="mobile-overlay"></div>

<!-- Main Sidebar Container -->
<div class="sidebar-container">
  <div [class.collapsed]="isCollapsed" [class.mobile-open]="isMobileOpen" class="sidebar main-sidebar">
    <!-- Header with hamburger -->
    <div class="sidebar-header">
      <div *ngIf="!isCollapsed" class="logo">
        <img alt="Chainmatic" class="logo-image" src="images/chainmatic-portal.png">
      </div>
      <p-button
        (onClick)="toggleSidebar()"
        [text]="true"
        class="hamburger-btn"
        icon="pi pi-bars"
        size="small">
      </p-button>
    </div>

    <!-- Navigation Items -->
    <div class="nav-section">
      <div class="nav-items">
        <div
          (click)="selectFeature(item)"
          *ngFor="let item of navItems"
          [class.active]="activeNav === item.id"
          class="nav-item">

          <!-- Icon -->
          <div [class.active-icon]="activeNav === item.id" class="nav-icon">
            <i [class]="item.icon"></i>
          </div>

          <!-- Name (only when expanded) -->
          <span *ngIf="!isCollapsed" class="nav-name">{{ item.name }}</span>
        </div>
      </div>
    </div>

    <!-- User Section -->
    <div *ngIf="currentUser" class="user-section">
      <!-- Settings Button (only if there are settings) -->
      <div *ngIf="hasSettings" class="settings-button-container">
        <button
          (click)="toggleSettingsPanel()"
          [class.active]="isSettingsActive"
          [title]="isCollapsed ? 'Settings' : ''"
          class="settings-button">
          <i class="pi pi-cog"></i>
          <span *ngIf="!isCollapsed" class="settings-text">Settings</span>
        </button>
      </div>

      <div *ngIf="!isCollapsed" class="user-info">
        <div class="user-avatar">
          <span>{{ getUserInitials() }}</span>
        </div>
        <div class="user-details">
          <span class="user-email">{{ currentUser.email }}</span>
        </div>
      </div>

      <div class="user-actions">
        <button
          (click)="logout()"
          [title]="isCollapsed ? 'Logout' : ''"
          class="logout-button">
          <i class="pi pi-sign-out"></i>
          <span *ngIf="!isCollapsed" class="logout-text">Logout</span>
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Nested Sidebar Container (Settings) -->
<div *ngIf="isSettingsActive && hasSettings" [class.main-collapsed]="isCollapsed" class="nested-sidebar-container">
  <div [class.collapsed]="isNestedSidebarCollapsed" class="sidebar second-sidebar">
    <!-- Collapse Button -->
    <div (click)="toggleNestedSidebar()" class="nested-collapse-btn">
      <i [class.rotated]="!isNestedSidebarCollapsed" class="pi pi-chevron-right"></i>
    </div>

    <!-- Settings Navigation Items -->
    <div *ngIf="!isNestedSidebarCollapsed" class="nav-section compact">
      <div class="nav-items">
        <div
          (click)="selectFeature(item)"
          *ngFor="let item of settingsItems"
          [class.active]="activeNav === item.id"
          class="nav-item settings-nav-item">

          <!-- Icon -->
          <div [class.active-icon]="activeNav === item.id" class="nav-icon">
            <i [class]="item.icon"></i>
          </div>

          <!-- Name -->
          <span class="nav-name">{{ item.name }}</span>
        </div>
      </div>
    </div>

    <!-- Collapsed state - show only icons -->
    <div *ngIf="isNestedSidebarCollapsed" class="nav-section compact collapsed-icons">
      <div class="nav-items">
        <div
          (click)="selectFeature(item)"
          *ngFor="let item of settingsItems"
          [class.active]="activeNav === item.id"
          [title]="item.name"
          class="nav-item settings-nav-item collapsed-item">

          <!-- Icon only -->
          <div [class.active-icon]="activeNav === item.id" class="nav-icon">
            <i [class]="item.icon"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Nested Sidebar Container (Parent Features) -->
<div *ngIf="hasNestedNavigation && !isSettingsActive" [class.main-collapsed]="isCollapsed" class="nested-sidebar-container">
  <div [class.collapsed]="isNestedSidebarCollapsed" class="sidebar second-sidebar">
    <!-- Collapse Button -->
    <div (click)="toggleNestedSidebar()" class="nested-collapse-btn">
      <i [class.rotated]="!isNestedSidebarCollapsed" class="pi pi-chevron-right"></i>
    </div>

    <!-- Parent Feature Navigation Items -->
    <div *ngIf="!isNestedSidebarCollapsed" class="nav-section compact">
      <div class="nav-items">
        <!-- Parent Feature Item -->
        <div
          (click)="selectFeature(currentParentFeature!)"
          *ngIf="currentParentFeature"
          [class.active]="activeNav === currentParentFeature.id"
          class="nav-item parent-nav-item">

          <!-- Icon -->
          <div [class.active-icon]="activeNav === currentParentFeature.id" class="nav-icon">
            <i [class]="currentParentFeature.icon"></i>
          </div>

          <!-- Name -->
          <span class="nav-name">{{ currentParentFeature.name }}</span>
        </div>

        <!-- Child Features -->
        <div
          (click)="selectFeature(item)"
          *ngFor="let item of getChildFeatures()"
          [class.active]="activeNav === item.id"
          class="nav-item child-nav-item">

          <!-- Icon -->
          <div [class.active-icon]="activeNav === item.id" class="nav-icon">
            <i [class]="item.icon"></i>
          </div>

          <!-- Name -->
          <span class="nav-name">{{ item.name }}</span>
        </div>
      </div>
    </div>

    <!-- Collapsed state - show only icons -->
    <div *ngIf="isNestedSidebarCollapsed" class="nav-section compact collapsed-icons">
      <div class="nav-items">
        <!-- Parent Feature Icon -->
        <div
          (click)="selectFeature(currentParentFeature!)"
          *ngIf="currentParentFeature"
          [class.active]="activeNav === currentParentFeature.id"
          [title]="currentParentFeature.name"
          class="nav-item parent-nav-item collapsed-item">

          <!-- Icon only -->
          <div [class.active-icon]="activeNav === currentParentFeature.id" class="nav-icon">
            <i [class]="currentParentFeature.icon"></i>
          </div>
        </div>

        <!-- Child Features Icons -->
        <div
          (click)="selectFeature(item)"
          *ngFor="let item of getChildFeatures()"
          [class.active]="activeNav === item.id"
          [title]="item.name"
          class="nav-item child-nav-item collapsed-item">

          <!-- Icon only -->
          <div [class.active-icon]="activeNav === item.id" class="nav-icon">
            <i [class]="item.icon"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
