<!-- Mobile Overlay -->
<div (click)="closeMobileSidebar()" *ngIf="isMobileOpen" class="mobile-overlay"></div>

<!-- Main Sidebar Container -->
<div class="sidebar-container">
  <div [class.collapsed]="isCollapsed" [class.mobile-open]="isMobileOpen" class="sidebar main-sidebar">
    <!-- Header with hamburger -->
    <div class="sidebar-header">
      <div *ngIf="!isCollapsed" class="logo">
        <img alt="Chainmatic" class="logo-image" src="images/chainmatic-portal.png">
      </div>
      <p-button
        (onClick)="toggleSidebar()"
        [text]="true"
        class="hamburger-btn"
        icon="pi pi-bars"
        size="small">
      </p-button>
    </div>

    <!-- Navigation Items -->
    <div class="nav-section">
      <div class="nav-items">
        <div
          (click)="selectFeature(item)"
          *ngFor="let item of navItems"
          [class.active]="activeNav === item.id"
          [class.has-nested]="!isMobile() && hasChildFeatures(item.id)"
          class="nav-item">

          <!-- Icon -->
          <div [class.active-icon]="activeNav === item.id" class="nav-icon">
            <i [class]="item.icon"></i>
          </div>

          <!-- Name (only when expanded) -->
          <span *ngIf="!isCollapsed" class="nav-name">{{ item.name }}</span>

          <!-- Nested indicator (only when expanded and has children) -->
          <div *ngIf="!isCollapsed && !isMobile() && hasChildFeatures(item.id)" class="nested-indicator">
            <i class="pi pi-chevron-right"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- User Section -->
    <div *ngIf="currentUser" class="user-section">
      <!-- Settings Button (only if there are settings) -->
      <div *ngIf="hasSettings" class="settings-button-container">
        <button
          (click)="toggleSettingsPanel()"
          [class.active]="isSettingsActive"
          [title]="isCollapsed ? 'Settings' : ''"
          class="settings-button">
          <i class="pi pi-cog"></i>
          <span *ngIf="!isCollapsed" class="settings-text">Settings</span>
        </button>
      </div>

      <div *ngIf="!isCollapsed" class="user-info">
        <div class="user-avatar">
          <span>{{ getUserInitials() }}</span>
        </div>
        <div class="user-details">
          <span class="user-email">{{ currentUser.email }}</span>
        </div>
      </div>

      <div class="user-actions">
        <button
          (click)="logout()"
          [title]="isCollapsed ? 'Logout' : ''"
          class="logout-button">
          <i class="pi pi-sign-out"></i>
          <span *ngIf="!isCollapsed" class="logout-text">Logout</span>
        </button>
      </div>
    </div>
  </div>
</div>

<!-- Unified Nested Sidebar Container -->
<div *ngIf="hasNestedNavigation" [class.main-collapsed]="isCollapsed" class="nested-sidebar-container">
  <div [class.collapsed]="isNestedSidebarCollapsed" class="sidebar second-sidebar">
    <!-- Collapse Button -->
    <div (click)="toggleNestedSidebar()" class="nested-collapse-btn">
      <i [class.rotated]="!isNestedSidebarCollapsed" class="pi pi-chevron-right"></i>
    </div>

    <!-- Navigation Title (when expanded) -->
    <div *ngIf="!isNestedSidebarCollapsed" class="nested-nav-header">
      <span class="nested-nav-title">{{ nestedNavigationTitle }}</span>
    </div>

    <!-- Navigation Items (expanded) -->
    <div *ngIf="!isNestedSidebarCollapsed" class="nav-section compact">
      <div class="nav-items">
        <div
          (click)="selectFeature(item)"
          *ngFor="let item of nestedNavigationItems"
          [class.active]="activeNestedNav === item.id"
          class="nav-item nested-nav-item">

          <!-- Icon -->
          <div [class.active-icon]="activeNestedNav === item.id" class="nav-icon">
            <i [class]="item.icon"></i>
          </div>

          <!-- Name -->
          <span class="nav-name">{{ item.name }}</span>
        </div>
      </div>
    </div>

    <!-- Collapsed state - show only icons -->
    <div *ngIf="isNestedSidebarCollapsed" class="nav-section compact collapsed-icons">
      <div class="nav-items">
        <div
          (click)="selectFeature(item)"
          *ngFor="let item of nestedNavigationItems"
          [class.active]="activeNav === item.id"
          [title]="item.name"
          class="nav-item nested-nav-item collapsed-item">

          <!-- Icon only -->
          <div [class.active-icon]="activeNav === item.id" class="nav-icon">
            <i [class]="item.icon"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>


