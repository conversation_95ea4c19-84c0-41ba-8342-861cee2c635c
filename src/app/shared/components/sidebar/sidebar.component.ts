import {
  Component,
  ElementRef,
  EventEmitter,
  HostListener,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { ButtonModule } from 'primeng/button';
import { filter, Subject, takeUntil } from 'rxjs';
import { AuthUser, ConfigService, SupabaseService } from '../../../core';
import { PortalFeature, PortalFeatureDefinition } from '../../../core/types';

@Component({
  selector: 'chm-sidebar',
  standalone: true,
  imports: [CommonModule, ButtonModule],
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.css'],
})
export class SidebarComponent implements OnInit, OnDestroy {
  isCollapsed = true;
  isMobileOpen = false;
  activeNav: string | null = null;
  activeNestedNav: string | null = null; // For tracking active item in nested navigation
  currentUser: AuthUser | null = null;
  isNestedSidebarCollapsed = true;
  currentNestedContext: 'settings' | 'parent-features' | null = null;
  @Output() stateChange = new EventEmitter<{
    collapsed: boolean;
    showSettings: boolean;
    nestedCollapsed?: boolean;
  }>();
  // Navigation items (former automations as nav links)
  private destroy$ = new Subject<void>();

  constructor(
    private router: Router,
    private supabaseService: SupabaseService,
    private configService: ConfigService,
    private elementRef: ElementRef,
  ) {}

  // Navigation items from config - only top-level features (no parent)
  get navItems(): PortalFeatureDefinition[] {
    return this.configService.topLevelNavigation;
  }

  get settingsItems(): PortalFeatureDefinition[] {
    return this.configService.settingsNavigation;
  }

  get hasSettings(): boolean {
    return this.configService.hasSettings;
  }

  get isSettingsActive(): boolean {
    return this.router.url.startsWith('/settings');
  }

  get hasNestedNavigation(): boolean {
    console.log(
      '🔍 hasNestedNavigation check - currentNestedContext:',
      this.currentNestedContext,
    );
    const result = this.currentNestedContext !== null;
    console.log('📊 hasNestedNavigation result:', result);
    return result;
  }

  get nestedNavigationItems(): PortalFeatureDefinition[] {
    switch (this.currentNestedContext) {
      case 'settings':
        return this.settingsItems;
      case 'parent-features':
        return this.getParentFeatureNavigation();
      default:
        return [];
    }
  }

  get nestedNavigationTitle(): string {
    switch (this.currentNestedContext) {
      case 'settings':
        return 'Settings';
      case 'parent-features':
        const parentFeature = this.getCurrentParentFeature();
        return parentFeature ? parentFeature.name : 'Navigation';
      default:
        return '';
    }
  }

  toggleSettingsPanel() {
    if (this.isSettingsActive) {
      // If we're already on settings, navigate to first regular nav item
      const firstRegularNav = this.navItems[0];
      if (firstRegularNav) {
        this.router.navigate([firstRegularNav.route]);
      }
    } else {
      // Navigate to first settings item
      const firstSettingsNav = this.settingsItems[0];
      if (firstSettingsNav) {
        this.router.navigate([firstSettingsNav.route]);
      }
    }
  }

  toggleNestedSidebar() {
    this.isNestedSidebarCollapsed = !this.isNestedSidebarCollapsed;
    this.emitStateChange();
  }

  ngOnInit() {
    // Set active nav based on current route
    this.updateActiveNavFromRoute();

    // Listen to router events to update active nav
    this.router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        takeUntil(this.destroy$),
      )
      .subscribe(() => {
        this.updateActiveNavFromRoute();
      });

    // Subscribe to auth state
    this.supabaseService.authState$
      .pipe(takeUntil(this.destroy$))
      .subscribe((authState: any) => {
        this.currentUser = authState.user;
      });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  toggleSidebar() {
    console.log('🔄 Toggle sidebar called, isMobile:', this.isMobile());
    if (this.isMobile()) {
      this.isMobileOpen = !this.isMobileOpen;
      console.log('📱 Mobile sidebar toggled:', this.isMobileOpen);
    } else {
      this.isCollapsed = !this.isCollapsed;
      console.log('🖥️ Desktop sidebar toggled:', this.isCollapsed);
      this.emitStateChange();
    }
  }

  closeMobileSidebar() {
    console.log('❌ Close mobile sidebar called');
    this.isMobileOpen = false;
  }

  selectFeature(feature: PortalFeatureDefinition) {
    // If selecting from nested sidebar, update activeNestedNav
    if (this.hasNestedNavigation) {
      this.activeNestedNav = feature.id;

      // If it's a child feature, keep parent as activeNav
      if (feature.parentFeature) {
        this.activeNav = feature.parentFeature;
      } else {
        this.activeNav = feature.id;
      }
    } else {
      this.activeNav = feature.id;
    }

    this.router.navigate([feature.route]);

    // If selecting from nested sidebar, collapse main sidebar on desktop
    if (this.hasNestedNavigation && !this.isMobile()) {
      this.isCollapsed = true;
      this.emitStateChange();
    }

    // Close mobile sidebar after navigation
    if (this.isMobile()) {
      this.closeMobileSidebar();
    }
  }

  isMobile(): boolean {
    return window.innerWidth <= 768;
  }

  async logout() {
    await this.supabaseService.signOut();
    this.router.navigate(['/auth/login']);
  }

  getUserInitials(): string {
    if (!this.currentUser) return 'U';
    const name = this.currentUser.name || this.currentUser.email || 'User';
    return name
      .split(' ')
      .map((n: string) => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  }

  // Click outside handler to collapse both sidebars
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event) {
    const target = event.target as HTMLElement;

    // Check if click is outside both sidebars
    const isClickInsideSidebar = this.elementRef.nativeElement.contains(target);
    const isClickOnHamburger =
      target.closest('.hamburger-btn') || target.closest('.mobile-menu-btn');

    if (!isClickInsideSidebar && !isClickOnHamburger && !this.isMobile()) {
      // Collapse both sidebars when clicking outside
      if (!this.isCollapsed || !this.isNestedSidebarCollapsed) {
        this.isCollapsed = true;
        this.isNestedSidebarCollapsed = true;
        this.emitStateChange();
      }
    }
  }

  hasChildFeatures(id: PortalFeature) {
    return this.configService.hasChildFeatures(id);
  }

  private getParentFeatureNavigation(): PortalFeatureDefinition[] {
    console.log('🔍 getParentFeatureNavigation called');
    console.log('📍 activeNav:', this.activeNav);
    console.log('🏗️ currentNestedContext:', this.currentNestedContext);

    const parentFeature = this.getCurrentParentFeature();
    console.log('👨 parentFeature:', parentFeature);

    if (!parentFeature) {
      console.log('❌ No parent feature found');
      return [];
    }

    const childFeatures = this.configService.getChildFeatures(parentFeature.id);
    console.log('👶 childFeatures:', childFeatures);

    // Create a dashboard item for the parent feature
    const dashboardItem: PortalFeatureDefinition = {
      ...parentFeature,
      name: 'Dashboard',
      icon: 'pi pi-home',
    };

    const result = [dashboardItem, ...childFeatures];
    console.log('📋 Final navigation items:', result);
    return result;
  }

  private getCurrentParentFeature(): PortalFeatureDefinition | null {
    console.log(
      '🔍 getCurrentParentFeature called with activeNav:',
      this.activeNav,
    );

    if (!this.activeNav) {
      console.log('❌ No activeNav');
      return null;
    }

    // activeNav is always set to parent feature ID, so just find it
    const parentFeature = this.configService.navigation.find(
      (f) => f.id === this.activeNav,
    );

    console.log('👨 Found parentFeature:', parentFeature);

    // Check if this feature has children
    const hasChildren =
      parentFeature && this.configService.hasChildFeatures(parentFeature.id);
    console.log('👶 Has children:', hasChildren);

    if (hasChildren) {
      return parentFeature;
    }

    return null;
  }

  private emitStateChange() {
    this.stateChange.emit({
      collapsed: this.isCollapsed,
      showSettings: this.isSettingsActive,
      nestedCollapsed: this.isNestedSidebarCollapsed,
    });
  }

  private updateActiveNavFromRoute() {
    const currentUrl = this.router.url;
    console.log('🔄 updateActiveNavFromRoute called with URL:', currentUrl);

    // Reset state
    this.activeNav = null;
    this.activeNestedNav = null;
    this.currentNestedContext = null;
    console.log('🔄 Reset state - activeNav: null, activeNestedNav: null, currentNestedContext: null');

    // Get all top-level features (main navigation)
    const topLevelFeatures = this.configService.topLevelNavigation;
    const settingsFeatures = this.configService.settingsNavigation;

    console.log(
      '📋 Top-level features:',
      topLevelFeatures.map((f) => ({ id: f.id, route: f.route })),
    );

    // Check settings first
    if (currentUrl.startsWith('/settings')) {
      console.log('⚙️ Settings route detected');
      this.currentNestedContext = 'settings';

      // Find which settings feature matches
      const matchingSettings = settingsFeatures.find(
        (feature) =>
          currentUrl === feature.route ||
          currentUrl.startsWith(feature.route + '/'),
      );

      if (matchingSettings) {
        this.activeNestedNav = matchingSettings.id;
        console.log('⚙️ Found matching settings feature:', matchingSettings.id);
      } else if (settingsFeatures.length > 0) {
        // Fallback to first settings feature
        this.activeNestedNav = settingsFeatures[0].id;
        console.log('⚙️ No exact match, using first settings feature:', settingsFeatures[0].id);
      }

      console.log(
        '⚙️ Settings - activeNestedNav:',
        this.activeNestedNav,
        'context:',
        this.currentNestedContext,
      );
      this.emitStateChange();
      return;
    }

    // Check top-level features
    for (const feature of topLevelFeatures) {
      console.log(
        '🔍 Checking feature:',
        feature.id,
        'route:',
        feature.route,
        'against URL:',
        currentUrl,
      );

      if (currentUrl.startsWith(feature.route)) {
        console.log('✅ Match found for feature:', feature.id);
        this.activeNav = feature.id;

        // Check if this feature has children - if yes, show nested navigation
        const hasChildren = this.configService.hasChildFeatures(feature.id);

        console.log('👶 Has children:', hasChildren);

        if (hasChildren) {
          this.currentNestedContext = 'parent-features';
          console.log('🏗️ Setting parent-features context because feature has children');

          // Determine which nested item should be active
          if (currentUrl === feature.route) {
            // We're on the parent route, so Dashboard should be active
            this.activeNestedNav = feature.id;
            console.log('📍 On parent route, setting activeNestedNav to Dashboard:', feature.id);
          } else {
            // We're on a child route, find which child
            const allFeatures = this.configService.navigation;
            const matchingChild = allFeatures.find(f =>
              f.parentFeature === feature.id && currentUrl.startsWith(f.route)
            );
            if (matchingChild) {
              this.activeNestedNav = matchingChild.id;
              console.log('📍 On child route, setting activeNestedNav to child:', matchingChild.id);
            } else {
              this.activeNestedNav = feature.id; // fallback to Dashboard
              console.log('📍 No matching child found, fallback to Dashboard:', feature.id);
            }
          }
        }

        console.log(
          '📍 Final state - activeNav:',
          this.activeNav,
          'activeNestedNav:',
          this.activeNestedNav,
          'context:',
          this.currentNestedContext,
        );
        this.emitStateChange();
        return;
      }
    }

    console.log('❌ No matches found, using fallback');
    // Fallback to first feature if nothing matches
    if (topLevelFeatures.length > 0) {
      this.activeNav = topLevelFeatures[0].id;
    }

    console.log(
      '📍 Fallback state - activeNav:',
      this.activeNav,
      'context:',
      this.currentNestedContext,
    );
    this.emitStateChange();
  }

  // Method removed - logic simplified
}
