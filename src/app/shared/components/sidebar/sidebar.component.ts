import {
  Component,
  ElementRef,
  EventEmitter,
  HostListener,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { ButtonModule } from 'primeng/button';
import { filter, Subject, takeUntil } from 'rxjs';
import { AuthUser, ConfigService, SupabaseService } from '../../../core';
import { PortalFeature, PortalFeatureDefinition } from '../../../core/types';

@Component({
  selector: 'chm-sidebar',
  standalone: true,
  imports: [CommonModule, ButtonModule],
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.css'],
})
export class SidebarComponent implements OnInit, OnDestroy {
  isCollapsed = true;
  isMobileOpen = false;
  activeNav: string | null = null;
  currentUser: AuthUser | null = null;
  isNestedSidebarCollapsed = true;
  currentNestedContext: 'settings' | 'parent-features' | null = null;
  @Output() stateChange = new EventEmitter<{
    collapsed: boolean;
    showSettings: boolean;
    nestedCollapsed?: boolean;
  }>();
  // Navigation items (former automations as nav links)
  private destroy$ = new Subject<void>();

  constructor(
    private router: Router,
    private supabaseService: SupabaseService,
    private configService: ConfigService,
    private elementRef: ElementRef,
  ) {}

  // Navigation items from config - only top-level features (no parent)
  get navItems(): PortalFeatureDefinition[] {
    const topLevelFeatures = this.configService.topLevelNavigation;

    // On mobile, include nested items in main navigation
    if (this.isMobile() && this.hasNestedNavigation) {
      return [...topLevelFeatures, ...this.nestedNavigationItems];
    }

    return topLevelFeatures;
  }

  get settingsItems(): PortalFeatureDefinition[] {
    return this.configService.settingsNavigation;
  }

  get hasSettings(): boolean {
    return this.configService.hasSettings;
  }

  get isSettingsActive(): boolean {
    return this.router.url.startsWith('/settings');
  }

  get hasNestedNavigation(): boolean {
    return this.currentNestedContext !== null;
  }

  get nestedNavigationItems(): PortalFeatureDefinition[] {
    switch (this.currentNestedContext) {
      case 'settings':
        return this.settingsItems;
      case 'parent-features':
        return this.getParentFeatureNavigation();
      default:
        return [];
    }
  }

  get nestedNavigationTitle(): string {
    switch (this.currentNestedContext) {
      case 'settings':
        return 'Settings';
      case 'parent-features':
        const parentFeature = this.getCurrentParentFeature();
        return parentFeature ? parentFeature.name : 'Navigation';
      default:
        return '';
    }
  }

  toggleSettingsPanel() {
    if (this.isSettingsActive) {
      // If we're already on settings, navigate to first regular nav item
      const firstRegularNav = this.navItems[0];
      if (firstRegularNav) {
        this.router.navigate([firstRegularNav.route]);
      }
    } else {
      // Navigate to first settings item
      const firstSettingsNav = this.settingsItems[0];
      if (firstSettingsNav) {
        this.router.navigate([firstSettingsNav.route]);
      }
    }
  }

  toggleNestedSidebar() {
    this.isNestedSidebarCollapsed = !this.isNestedSidebarCollapsed;
    this.emitStateChange();
  }

  ngOnInit() {
    // Set active nav based on current route
    this.updateActiveNavFromRoute();

    // Listen to router events to update active nav
    this.router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        takeUntil(this.destroy$),
      )
      .subscribe(() => {
        this.updateActiveNavFromRoute();
      });

    // Subscribe to auth state
    this.supabaseService.authState$
      .pipe(takeUntil(this.destroy$))
      .subscribe((authState: any) => {
        this.currentUser = authState.user;
      });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  toggleSidebar() {
    console.log('🔄 Toggle sidebar called, isMobile:', this.isMobile());
    if (this.isMobile()) {
      this.isMobileOpen = !this.isMobileOpen;
      console.log('📱 Mobile sidebar toggled:', this.isMobileOpen);
    } else {
      this.isCollapsed = !this.isCollapsed;
      console.log('🖥️ Desktop sidebar toggled:', this.isCollapsed);
      this.emitStateChange();
    }
  }

  closeMobileSidebar() {
    console.log('❌ Close mobile sidebar called');
    this.isMobileOpen = false;
  }

  selectFeature(feature: PortalFeatureDefinition) {
    this.activeNav = feature.id;
    this.router.navigate([feature.route]);

    // If selecting from nested sidebar, collapse main sidebar on desktop
    if (this.hasNestedNavigation && !this.isMobile()) {
      this.isCollapsed = true;
      this.emitStateChange();
    }

    // Close mobile sidebar after navigation
    if (this.isMobile()) {
      this.closeMobileSidebar();
    }
  }

  isMobile(): boolean {
    return window.innerWidth <= 768;
  }

  async logout() {
    await this.supabaseService.signOut();
    this.router.navigate(['/auth/login']);
  }

  getUserInitials(): string {
    if (!this.currentUser) return 'U';
    const name = this.currentUser.name || this.currentUser.email || 'User';
    return name
      .split(' ')
      .map((n: string) => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  }

  // Click outside handler to collapse both sidebars
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event) {
    const target = event.target as HTMLElement;

    // Check if click is outside both sidebars
    const isClickInsideSidebar = this.elementRef.nativeElement.contains(target);
    const isClickOnHamburger =
      target.closest('.hamburger-btn') || target.closest('.mobile-menu-btn');

    if (!isClickInsideSidebar && !isClickOnHamburger && !this.isMobile()) {
      // Collapse both sidebars when clicking outside
      if (!this.isCollapsed || !this.isNestedSidebarCollapsed) {
        this.isCollapsed = true;
        this.isNestedSidebarCollapsed = true;
        this.emitStateChange();
      }
    }
  }

  hasChildFeatures(id: PortalFeature) {
    return this.configService.hasChildFeatures(id);
  }

  private getParentFeatureNavigation(): PortalFeatureDefinition[] {
    const parentFeature = this.getCurrentParentFeature();
    if (!parentFeature) return [];

    const childFeatures = this.configService.getChildFeatures(parentFeature.id);

    // Create a dashboard item for the parent feature
    const dashboardItem: PortalFeatureDefinition = {
      ...parentFeature,
      name: 'Dashboard',
      icon: 'pi pi-home',
    };

    return [dashboardItem, ...childFeatures];
  }

  private getCurrentParentFeature(): PortalFeatureDefinition | null {
    if (!this.activeNav) return null;

    // activeNav is always set to parent feature ID, so just find it
    const parentFeature = this.configService.navigation.find(
      (f) => f.id === this.activeNav,
    );

    // Check if this feature has children
    if (parentFeature && this.configService.hasChildFeatures(parentFeature.id)) {
      return parentFeature;
    }

    return null;
  }

  private emitStateChange() {
    this.stateChange.emit({
      collapsed: this.isCollapsed,
      showSettings: this.isSettingsActive,
      nestedCollapsed: this.isNestedSidebarCollapsed,
    });
  }

  private updateActiveNavFromRoute() {
    const currentUrl = this.router.url;
    console.log('🔄 updateActiveNavFromRoute called with URL:', currentUrl);

    // Combine all navigation items (top-level + settings)
    const allNavItems = [...this.navItems, ...this.settingsItems];
    const allFeatures = this.configService.navigation; // This includes all features including nested

    console.log('📋 All features:', allFeatures.map(f => ({ id: f.id, route: f.route, parent: f.parentFeature })));

    // First, try to find exact match in all features (including nested ones)
    let matchingFeature = allFeatures.find((item) => {
      const routePath = item.route.startsWith('/')
        ? item.route.substring(1)
        : item.route;
      const currentPath = currentUrl.startsWith('/')
        ? currentUrl.substring(1)
        : currentUrl;

      return currentPath === routePath;
    });

    console.log('🎯 Exact matching feature:', matchingFeature);

    // Determine nested context and active nav based on current route
    if (matchingFeature) {
      console.log('✅ Found matching feature:', matchingFeature.id, 'parent:', matchingFeature.parentFeature);

      // If it's a child feature, set parent as active in main nav
      if (matchingFeature.parentFeature) {
        this.activeNav = matchingFeature.parentFeature;
        console.log('👶 Child feature detected, setting activeNav to parent:', matchingFeature.parentFeature);
      } else {
        this.activeNav = matchingFeature.id;
        console.log('👨 Parent feature detected, setting activeNav to:', matchingFeature.id);
      }

      const newNestedContext = this.determineNestedContext(
        matchingFeature,
        currentUrl,
      );

      console.log('🏗️ New nested context:', newNestedContext);

      // Auto-collapse nested sidebar if context changes to null
      if (this.currentNestedContext && !newNestedContext) {
        this.isNestedSidebarCollapsed = true;
      }

      this.currentNestedContext = newNestedContext;
    } else {
      console.log('❌ No exact match found, trying fallback logic');

      // Fallback: try to match by route prefix for main navigation
      const matchingNav = allNavItems.find((item) => {
        const routePath = item.route.startsWith('/')
          ? item.route.substring(1)
          : item.route;
        const currentPath = currentUrl.startsWith('/')
          ? currentUrl.substring(1)
          : currentUrl;

        console.log('🔍 Checking route:', routePath, 'against path:', currentPath);

        // For parent features, check if current path starts with parent route
        const matches = currentPath === routePath || currentPath.startsWith(routePath + '/');
        console.log('🎯 Match result:', matches);
        return matches;
      });

      console.log('📍 Fallback matching nav:', matchingNav);

      if (matchingNav) {
        this.activeNav = matchingNav.id;
        console.log('✅ Setting activeNav to:', matchingNav.id);

        // For fallback matches, we need to check if we're on a child route
        const currentPath = currentUrl.startsWith('/') ? currentUrl.substring(1) : currentUrl;
        const routePath = matchingNav.route.startsWith('/') ? matchingNav.route.substring(1) : matchingNav.route;

        // If current path is longer than route path, we might be on a child route
        if (currentPath.startsWith(routePath + '/') && this.configService.hasChildFeatures(matchingNav.id)) {
          console.log('🏗️ Detected child route, setting parent-features context');
          this.currentNestedContext = 'parent-features';
        } else {
          const newNestedContext = this.determineNestedContext(matchingNav, currentUrl);
          this.currentNestedContext = newNestedContext;
        }

        // Auto-collapse nested sidebar if context changes to null
        if (this.currentNestedContext && !this.currentNestedContext) {
          this.isNestedSidebarCollapsed = true;
        }
      } else {
        console.log('❌ No fallback match found either');
        // Final fallback to first top-level nav item
        const topLevelFeatures = this.configService.topLevelNavigation;
        if (topLevelFeatures.length > 0) {
          this.activeNav = topLevelFeatures[0].id;
        }
        this.currentNestedContext = null;
        this.isNestedSidebarCollapsed = true;
      }
    }

    // Emit state change to update main content margin
    this.emitStateChange();
  }

  private determineNestedContext(
    feature: PortalFeatureDefinition,
    currentUrl: string,
  ): 'settings' | 'parent-features' | null {
    // Check if we're in settings
    if (currentUrl.startsWith('/settings')) {
      return 'settings';
    }

    // Check if this is a nested feature (child) or parent with children
    if (
      feature.parentFeature ||
      this.configService.hasChildFeatures(feature.id)
    ) {
      return 'parent-features';
    }

    return null;
  }
}
