import {
  Component,
  ElementRef,
  EventEmitter,
  HostListener,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { ButtonModule } from 'primeng/button';
import { filter, Subject, takeUntil } from 'rxjs';
import { AuthUser, ConfigService, SupabaseService } from '../../../core';
import { PortalFeature, PortalFeatureDefinition } from '../../../core/types';

@Component({
  selector: 'chm-sidebar',
  standalone: true,
  imports: [CommonModule, ButtonModule],
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.css'],
})
export class SidebarComponent implements OnInit, OnDestroy {
  isCollapsed = true;
  isMobileOpen = false;
  activeNav: string | null = null;
  currentUser: AuthUser | null = null;
  isNestedSidebarCollapsed = true;
  currentNestedContext: 'settings' | 'parent-features' | null = null;
  @Output() stateChange = new EventEmitter<{
    collapsed: boolean;
    showSettings: boolean;
    nestedCollapsed?: boolean;
  }>();
  // Navigation items (former automations as nav links)
  private destroy$ = new Subject<void>();

  constructor(
    private router: Router,
    private supabaseService: SupabaseService,
    private configService: ConfigService,
    private elementRef: ElementRef,
  ) {}

  // Navigation items from config - only top-level features (no parent)
  get navItems(): PortalFeatureDefinition[] {
    const topLevelFeatures = this.configService.topLevelNavigation;

    // On mobile, include nested items in main navigation
    if (this.isMobile() && this.hasNestedNavigation) {
      return [...topLevelFeatures, ...this.nestedNavigationItems];
    }

    return topLevelFeatures;
  }

  get settingsItems(): PortalFeatureDefinition[] {
    return this.configService.settingsNavigation;
  }

  get hasSettings(): boolean {
    return this.configService.hasSettings;
  }

  get isSettingsActive(): boolean {
    return this.router.url.startsWith('/settings');
  }

  get hasNestedNavigation(): boolean {
    return this.currentNestedContext !== null;
  }

  get nestedNavigationItems(): PortalFeatureDefinition[] {
    switch (this.currentNestedContext) {
      case 'settings':
        return this.settingsItems;
      case 'parent-features':
        return this.getParentFeatureNavigation();
      default:
        return [];
    }
  }

  get nestedNavigationTitle(): string {
    switch (this.currentNestedContext) {
      case 'settings':
        return 'Settings';
      case 'parent-features':
        const parentFeature = this.getCurrentParentFeature();
        return parentFeature ? parentFeature.name : 'Navigation';
      default:
        return '';
    }
  }

  toggleSettingsPanel() {
    if (this.isSettingsActive) {
      // If we're already on settings, navigate to first regular nav item
      const firstRegularNav = this.navItems[0];
      if (firstRegularNav) {
        this.router.navigate([firstRegularNav.route]);
      }
    } else {
      // Navigate to first settings item
      const firstSettingsNav = this.settingsItems[0];
      if (firstSettingsNav) {
        this.router.navigate([firstSettingsNav.route]);
      }
    }
  }

  toggleNestedSidebar() {
    this.isNestedSidebarCollapsed = !this.isNestedSidebarCollapsed;
    this.emitStateChange();
  }

  ngOnInit() {
    // Set active nav based on current route
    this.updateActiveNavFromRoute();

    // Listen to router events to update active nav
    this.router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        takeUntil(this.destroy$),
      )
      .subscribe(() => {
        this.updateActiveNavFromRoute();
      });

    // Subscribe to auth state
    this.supabaseService.authState$
      .pipe(takeUntil(this.destroy$))
      .subscribe((authState: any) => {
        this.currentUser = authState.user;
      });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  toggleSidebar() {
    console.log('🔄 Toggle sidebar called, isMobile:', this.isMobile());
    if (this.isMobile()) {
      this.isMobileOpen = !this.isMobileOpen;
      console.log('📱 Mobile sidebar toggled:', this.isMobileOpen);
    } else {
      this.isCollapsed = !this.isCollapsed;
      console.log('🖥️ Desktop sidebar toggled:', this.isCollapsed);
      this.emitStateChange();
    }
  }

  closeMobileSidebar() {
    console.log('❌ Close mobile sidebar called');
    this.isMobileOpen = false;
  }

  selectFeature(feature: PortalFeatureDefinition) {
    this.activeNav = feature.id;
    this.router.navigate([feature.route]);

    // If selecting from nested sidebar, collapse main sidebar on desktop
    if (this.hasNestedNavigation && !this.isMobile()) {
      this.isCollapsed = true;
      this.emitStateChange();
    }

    // Close mobile sidebar after navigation
    if (this.isMobile()) {
      this.closeMobileSidebar();
    }
  }

  isMobile(): boolean {
    return window.innerWidth <= 768;
  }

  async logout() {
    await this.supabaseService.signOut();
    this.router.navigate(['/auth/login']);
  }

  getUserInitials(): string {
    if (!this.currentUser) return 'U';
    const name = this.currentUser.name || this.currentUser.email || 'User';
    return name
      .split(' ')
      .map((n: string) => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  }

  // Click outside handler to collapse both sidebars
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event) {
    const target = event.target as HTMLElement;

    // Check if click is outside both sidebars
    const isClickInsideSidebar = this.elementRef.nativeElement.contains(target);
    const isClickOnHamburger =
      target.closest('.hamburger-btn') || target.closest('.mobile-menu-btn');

    if (!isClickInsideSidebar && !isClickOnHamburger && !this.isMobile()) {
      // Collapse both sidebars when clicking outside
      if (!this.isCollapsed || !this.isNestedSidebarCollapsed) {
        this.isCollapsed = true;
        this.isNestedSidebarCollapsed = true;
        this.emitStateChange();
      }
    }
  }

  hasChildFeatures(id: PortalFeature) {
    return this.configService.hasChildFeatures(id);
  }

  private getParentFeatureNavigation(): PortalFeatureDefinition[] {
    console.log('🔍 getParentFeatureNavigation called');
    console.log('📍 activeNav:', this.activeNav);
    console.log('🏗️ currentNestedContext:', this.currentNestedContext);

    const parentFeature = this.getCurrentParentFeature();
    console.log('👨 parentFeature:', parentFeature);

    if (!parentFeature) {
      console.log('❌ No parent feature found');
      return [];
    }

    const childFeatures = this.configService.getChildFeatures(parentFeature.id);
    console.log('👶 childFeatures:', childFeatures);

    // Create a dashboard item for the parent feature
    const dashboardItem: PortalFeatureDefinition = {
      ...parentFeature,
      name: 'Dashboard',
      icon: 'pi pi-home',
    };

    const result = [dashboardItem, ...childFeatures];
    console.log('📋 Final navigation items:', result);
    return result;
  }

  private getCurrentParentFeature(): PortalFeatureDefinition | null {
    console.log('🔍 getCurrentParentFeature called with activeNav:', this.activeNav);

    if (!this.activeNav) {
      console.log('❌ No activeNav');
      return null;
    }

    // activeNav is always set to parent feature ID, so just find it
    const parentFeature = this.configService.navigation.find(
      (f) => f.id === this.activeNav,
    );

    console.log('👨 Found parentFeature:', parentFeature);

    // Check if this feature has children
    const hasChildren = parentFeature && this.configService.hasChildFeatures(parentFeature.id);
    console.log('👶 Has children:', hasChildren);

    if (hasChildren) {
      return parentFeature;
    }

    return null;
  }

  private emitStateChange() {
    this.stateChange.emit({
      collapsed: this.isCollapsed,
      showSettings: this.isSettingsActive,
      nestedCollapsed: this.isNestedSidebarCollapsed,
    });
  }

  private updateActiveNavFromRoute() {
    const currentUrl = this.router.url;

    // Reset state
    this.activeNav = null;
    this.currentNestedContext = null;

    // Get all top-level features (main navigation)
    const topLevelFeatures = this.configService.topLevelNavigation;
    const settingsFeatures = this.configService.settingsNavigation;

    // Check settings first
    if (currentUrl.startsWith('/settings')) {
      this.currentNestedContext = 'settings';
      // Find which settings feature matches
      const matchingSettings = settingsFeatures.find(feature =>
        currentUrl === feature.route || currentUrl.startsWith(feature.route + '/')
      );
      if (matchingSettings) {
        this.activeNav = matchingSettings.id;
      }
      this.emitStateChange();
      return;
    }

    // Check top-level features
    for (const feature of topLevelFeatures) {
      if (currentUrl.startsWith(feature.route)) {
        this.activeNav = feature.id;

        // Check if this feature has children and we're on a child route
        if (this.configService.hasChildFeatures(feature.id) && currentUrl !== feature.route) {
          this.currentNestedContext = 'parent-features';
        }

        this.emitStateChange();
        return;
      }
    }

    // Fallback to first feature if nothing matches
    if (topLevelFeatures.length > 0) {
      this.activeNav = topLevelFeatures[0].id;
    }

    this.emitStateChange();
  }

// Method removed - logic simplified
}
