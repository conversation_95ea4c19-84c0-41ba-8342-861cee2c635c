import {
  Component,
  ElementRef,
  EventEmitter,
  HostListener,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { ButtonModule } from 'primeng/button';
import { filter, Subject, takeUntil } from 'rxjs';
import { AuthUser, ConfigService, SupabaseService } from '../../../core';
import { PortalFeature, PortalFeatureDefinition } from '../../../core/types';

@Component({
  selector: 'chm-sidebar',
  standalone: true,
  imports: [CommonModule, ButtonModule],
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.css'],
})
export class SidebarComponent implements OnInit, OnDestroy {
  isCollapsed = true;
  isMobileOpen = false;
  activeNav: string | null = null;
  currentUser: AuthUser | null = null;
  isNestedSidebarCollapsed = true;
  currentNestedContext: 'settings' | 'parent-features' | null = null;
  @Output() stateChange = new EventEmitter<{
    collapsed: boolean;
    showSettings: boolean;
    nestedCollapsed?: boolean;
  }>();
  // Navigation items (former automations as nav links)
  private destroy$ = new Subject<void>();

  constructor(
    private router: Router,
    private supabaseService: SupabaseService,
    private configService: ConfigService,
    private elementRef: ElementRef,
  ) {}

  // Navigation items from config - only top-level features (no parent)
  get navItems(): PortalFeatureDefinition[] {
    const regularNav = this.configService.regularNavigation;

    // Filter to only show features without a parent
    const topLevelFeatures = regularNav.filter(feature => !feature.parentFeature);

    // On mobile, include nested items in main navigation
    if (this.isMobile() && this.hasNestedNavigation) {
      return [...topLevelFeatures, ...this.nestedNavigationItems];
    }

    return topLevelFeatures;
  }

  get settingsItems(): PortalFeatureDefinition[] {
    return this.configService.settingsNavigation;
  }

  get hasSettings(): boolean {
    return this.configService.hasSettings;
  }

  get isSettingsActive(): boolean {
    return this.router.url.startsWith('/settings');
  }

  get hasNestedNavigation(): boolean {
    return this.currentNestedContext !== null;
  }

  get nestedNavigationItems(): PortalFeatureDefinition[] {
    switch (this.currentNestedContext) {
      case 'settings':
        return this.settingsItems;
      case 'parent-features':
        return this.getParentFeatureNavigation();
      default:
        return [];
    }
  }

  get nestedNavigationTitle(): string {
    switch (this.currentNestedContext) {
      case 'settings':
        return 'Settings';
      case 'parent-features':
        const parentFeature = this.getCurrentParentFeature();
        return parentFeature ? parentFeature.name : 'Navigation';
      default:
        return '';
    }
  }

  toggleSettingsPanel() {
    if (this.isSettingsActive) {
      // If we're already on settings, navigate to first regular nav item
      const firstRegularNav = this.navItems[0];
      if (firstRegularNav) {
        this.router.navigate([firstRegularNav.route]);
      }
    } else {
      // Navigate to first settings item
      const firstSettingsNav = this.settingsItems[0];
      if (firstSettingsNav) {
        this.router.navigate([firstSettingsNav.route]);
      }
    }
  }

  toggleNestedSidebar() {
    this.isNestedSidebarCollapsed = !this.isNestedSidebarCollapsed;
    this.emitStateChange();
  }

  ngOnInit() {
    // Set active nav based on current route
    this.updateActiveNavFromRoute();

    // Listen to router events to update active nav
    this.router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        takeUntil(this.destroy$),
      )
      .subscribe(() => {
        this.updateActiveNavFromRoute();
      });

    // Subscribe to auth state
    this.supabaseService.authState$
      .pipe(takeUntil(this.destroy$))
      .subscribe((authState: any) => {
        this.currentUser = authState.user;
      });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  toggleSidebar() {
    console.log('🔄 Toggle sidebar called, isMobile:', this.isMobile());
    if (this.isMobile()) {
      this.isMobileOpen = !this.isMobileOpen;
      console.log('📱 Mobile sidebar toggled:', this.isMobileOpen);
    } else {
      this.isCollapsed = !this.isCollapsed;
      console.log('🖥️ Desktop sidebar toggled:', this.isCollapsed);
      this.emitStateChange();
    }
  }

  closeMobileSidebar() {
    console.log('❌ Close mobile sidebar called');
    this.isMobileOpen = false;
  }

  selectFeature(feature: PortalFeatureDefinition) {
    this.activeNav = feature.id;
    this.router.navigate([feature.route]);

    // If selecting from nested sidebar, collapse main sidebar on desktop
    if (this.hasNestedNavigation && !this.isMobile()) {
      this.isCollapsed = true;
      this.emitStateChange();
    }

    // Close mobile sidebar after navigation
    if (this.isMobile()) {
      this.closeMobileSidebar();
    }
  }

  isMobile(): boolean {
    return window.innerWidth <= 768;
  }

  async logout() {
    await this.supabaseService.signOut();
    this.router.navigate(['/auth/login']);
  }

  getUserDisplayName(): string {
    if (!this.currentUser) return 'User';
    return this.currentUser.name || this.currentUser.email || 'User';
  }

  getUserInitials(): string {
    if (!this.currentUser) return 'U';
    const name = this.currentUser.name || this.currentUser.email || 'User';
    return name
      .split(' ')
      .map((n: string) => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  }

  // Click outside handler to collapse both sidebars
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event) {
    const target = event.target as HTMLElement;

    // Check if click is outside both sidebars
    const isClickInsideSidebar = this.elementRef.nativeElement.contains(target);
    const isClickOnHamburger =
      target.closest('.hamburger-btn') || target.closest('.mobile-menu-btn');

    if (!isClickInsideSidebar && !isClickOnHamburger && !this.isMobile()) {
      // Collapse both sidebars when clicking outside
      if (!this.isCollapsed || !this.isNestedSidebarCollapsed) {
        this.isCollapsed = true;
        this.isNestedSidebarCollapsed = true;
        this.emitStateChange();
      }
    }
  }

  hasChildFeatures(id: PortalFeature) {
    return this.configService.hasChildFeatures(id);
  }

  private getParentFeatureNavigation(): PortalFeatureDefinition[] {
    const parentFeature = this.getCurrentParentFeature();
    if (!parentFeature) return [];

    const childFeatures = this.configService.getChildFeatures(parentFeature.id);

    // Create a dashboard item for the parent feature
    const dashboardItem: PortalFeatureDefinition = {
      ...parentFeature,
      name: 'Dashboard',
      icon: 'pi pi-home',
    };

    return [dashboardItem, ...childFeatures];
  }

  private getCurrentParentFeature(): PortalFeatureDefinition | null {
    if (!this.activeNav) return null;

    // Check if current active nav is a child feature
    const currentFeature = this.configService.navigation.find(
      (f) => f.id === this.activeNav,
    );
    if (currentFeature?.parentFeature) {
      return (
        this.configService.navigation.find(
          (f) => f.id === currentFeature.parentFeature,
        ) || null
      );
    }

    // Check if current active nav is a parent feature with children
    if (
      currentFeature &&
      this.configService.hasChildFeatures(currentFeature.id)
    ) {
      return currentFeature;
    }

    return null;
  }

  private emitStateChange() {
    this.stateChange.emit({
      collapsed: this.isCollapsed,
      showSettings: this.isSettingsActive,
      nestedCollapsed: this.isNestedSidebarCollapsed,
    });
  }

  private updateActiveNavFromRoute() {
    const currentUrl = this.router.url;

    // Combine all navigation items (regular + settings + all features including nested)
    const allNavItems = [...this.navItems, ...this.settingsItems];
    const allFeatures = this.configService.navigation; // This includes nested features

    // First, try to find exact match in all features (including nested ones)
    let matchingFeature = allFeatures.find((item) => {
      const routePath = item.route.startsWith('/')
        ? item.route.substring(1)
        : item.route;
      const currentPath = currentUrl.startsWith('/')
        ? currentUrl.substring(1)
        : currentUrl;

      return currentPath === routePath;
    });

    // Determine nested context based on current route and matching feature
    if (matchingFeature) {
      this.activeNav = matchingFeature.id;
      const newNestedContext = this.determineNestedContext(
        matchingFeature,
        currentUrl,
      );

      // Auto-collapse nested sidebar if context changes to null
      if (this.currentNestedContext && !newNestedContext) {
        this.isNestedSidebarCollapsed = true;
      }

      this.currentNestedContext = newNestedContext;
    } else {
      // Fallback: try to match by route prefix for main navigation
      const matchingNav = allNavItems.find((item) => {
        const routePath = item.route.startsWith('/')
          ? item.route.substring(1)
          : item.route;
        const currentPath = currentUrl.startsWith('/')
          ? currentUrl.substring(1)
          : currentUrl;

        return (
          currentPath === routePath || currentPath.startsWith(routePath + '/')
        );
      });

      if (matchingNav) {
        this.activeNav = matchingNav.id;
        const newNestedContext = this.determineNestedContext(
          matchingNav,
          currentUrl,
        );

        // Auto-collapse nested sidebar if context changes to null
        if (this.currentNestedContext && !newNestedContext) {
          this.isNestedSidebarCollapsed = true;
        }

        this.currentNestedContext = newNestedContext;
      } else if (allNavItems.length > 0) {
        // Final fallback to first nav item
        this.activeNav = allNavItems[0].id;
        this.currentNestedContext = null;
        this.isNestedSidebarCollapsed = true;
      }
    }

    // Emit state change to update main content margin
    this.emitStateChange();
  }

  private determineNestedContext(
    feature: PortalFeatureDefinition,
    currentUrl: string,
  ): 'settings' | 'parent-features' | null {
    // Check if we're in settings
    if (currentUrl.startsWith('/settings')) {
      return 'settings';
    }

    // Check if this is a nested feature (child) or parent with children
    if (
      feature.parentFeature ||
      this.configService.hasChildFeatures(feature.id)
    ) {
      return 'parent-features';
    }

    return null;
  }
}
