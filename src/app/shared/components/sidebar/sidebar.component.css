/* Main Sidebar Container */
.sidebar-container {
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  z-index: 1000;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Nested Sidebar Container */
.nested-sidebar-container {
  position: fixed;
  left: 280px; /* Position next to main sidebar */
  top: 0;
  height: 100vh;
  z-index: 999;
  animation: slideInRight 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  opacity: 0;
  animation-fill-mode: forwards;
}

/* When main sidebar is collapsed, adjust nested sidebar position */
.nested-sidebar-container.main-collapsed {
  left: 70px;
}

.sidebar {
  height: 100vh;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.95) 0%, rgba(254, 236, 249, 0.8) 100%);
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 4px 0 24px rgba(0, 0, 0, 0.1);
}

/* Main Sidebar */
.main-sidebar {
  width: 280px;
}

.main-sidebar.collapsed {
  width: 70px;
}

/* Second Sidebar (Nested Navigation) */
.second-sidebar {
  width: 280px;
  border-left: 1px solid rgba(255, 255, 255, 0.1);
  background: linear-gradient(180deg, rgba(248, 250, 252, 0.95) 0%, rgba(241, 245, 249, 0.9) 100%);
  box-shadow: 2px 0 24px rgba(0, 0, 0, 0.08);
  position: relative;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.second-sidebar.collapsed {
  width: 60px;
}


/* Nested Navigation Header */
.nested-nav-header {
  padding: 1rem 0.75rem 0.5rem;
  border-bottom: 1px solid rgba(85, 33, 190, 0.1);
  margin-bottom: 0.5rem;
}

.nested-nav-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #5521be;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Compact navigation section for nested navigation */
.nav-section.compact {
  padding: 0.5rem 0.5rem 1rem;
}

.nav-section.compact .nav-items {
  gap: 0.25rem;
}

/* Nested Sidebar Collapse Button */
.nested-collapse-btn {
  position: absolute;
  right: -12px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(85, 33, 190, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.nested-collapse-btn:hover {
  background: rgba(85, 33, 190, 0.1);
  border-color: rgba(85, 33, 190, 0.3);
  transform: translateY(-50%) scale(1.1);
}

.nested-collapse-btn i {
  font-size: 0.75rem;
  color: #5521be;
  transition: transform 0.2s ease;
}

.nested-collapse-btn i.rotated {
  transform: rotate(180deg);
}

@keyframes slideInRight {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Header */
.sidebar-header {
  padding: 1.5rem 1.25rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 80px;
}

.main-sidebar.collapsed .sidebar-header {
  justify-content: center;
  padding: 1rem;
}

/* Settings Header in Second Sidebar */
.second-sidebar .sidebar-header {
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);
}

.settings-header-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.settings-header-icon {
  color: #5521be;
  font-size: 1.25rem;
}

.settings-header-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
}

.logo {
  display: flex;
  align-items: center;
}

.logo-image {
  height: 32px;
  width: auto;
  max-width: 180px;
  object-fit: contain;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
  transition: all 0.2s ease;
}

.logo-image:hover {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.15));
  transform: scale(1.02);
}

.hamburger-btn {
  opacity: 0.7;
  transition: all 0.2s;
  border-radius: 8px !important;
}

.hamburger-btn:hover {
  opacity: 1;
  background: rgba(85, 33, 190, 0.1) !important;
}

/* Navigation Section */
.nav-section {
  flex: 1;
  padding: 1rem 0.75rem;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

.nav-section::-webkit-scrollbar {
  width: 4px;
}

.nav-section::-webkit-scrollbar-track {
  background: transparent;
}

.nav-section::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

.nav-items {
  display: flex;
  flex-direction: column;
  gap: 0.375rem;
}

/* Settings Button */
.settings-button-container {
  margin-bottom: 1rem;
}

.settings-button {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 0.875rem; /* Slightly less vertical padding */
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  background: rgba(255, 255, 255, 0.5);
  color: #475569;
  font-size: 0.875rem;
  font-weight: 500;
  position: relative;
  box-sizing: border-box; /* Ensure proper box sizing */
}

.settings-button .pi-cog {
  font-size: 1rem; /* Smaller to fit within border box */
  display: inline-block;
  line-height: 1;
  vertical-align: middle;
}

.settings-button:hover {
  background: rgba(255, 255, 255, 0.8);
  border-color: rgba(85, 33, 190, 0.1);
  transform: translateX(2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  color: #1e293b;
}

.settings-button.active {
  background: rgba(85, 33, 190, 0.1);
  border-color: rgba(85, 33, 190, 0.2);
  color: #5521be;
  transform: translateX(2px);
}


/* Old settings menu styles removed - not used anymore */

/* Unified Nested Navigation Items (soft purple palette) */
.nested-nav-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  background: rgba(250, 245, 255, 0.6);
  border: 1px solid rgba(233, 213, 255, 0.4);
  padding: 0.625rem 0.75rem;
  font-size: 0.8rem;
  color: #7c3aed;
  border-radius: 0.5rem;
  cursor: pointer;
}

.nested-nav-item:hover {
  background: rgba(250, 245, 255, 0.9);
  border-color: rgba(196, 181, 253, 0.3);
  transform: translateX(1px);
  color: #7c3aed;
}

.nested-nav-item.active {
  background: rgba(139, 92, 246, 0.15);
  border-color: rgba(139, 92, 246, 0.6);
  color: #5b21b6;
  box-shadow: 0 2px 4px rgba(139, 92, 246, 0.2);
}

.nested-nav-item .nav-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.25rem;
  height: 1.25rem;
  color: #7c3aed;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.nested-nav-item .nav-icon i {
  font-size: 0.875rem;
}

.nested-nav-item.active .nav-icon {
  color: #5b21b6;
}

.nested-nav-item.active .nav-name {
  color: #5b21b6;
  font-weight: 600;
}

/* Settings-specific styles removed - now using unified .nested-nav-item styles */

/* Collapsed nested sidebar styles - New Design */
.collapsed-icons {
  padding: 1rem 0.5rem !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  gap: 0.5rem !important;
}

.collapsed-icons .nav-items {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  gap: 0.5rem !important;
}

/* Unified collapsed item styles (based on settings style) */
.nested-nav-item.collapsed-item {
  width: 36px !important;
  height: 36px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 10px !important;
  background: rgba(255, 255, 255, 0.7) !important;
  border: 1px solid rgba(255, 255, 255, 0.4) !important;
  cursor: pointer !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
  backdrop-filter: blur(8px) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05) !important;
  padding: 0 !important;
  margin-bottom: 0 !important;
  font-size: 0.9rem !important;
}

.nested-nav-item.collapsed-item .nav-icon {
  margin: 0 !important;
  color: #7c3aed !important;
  width: auto !important;
  height: auto !important;
}

.nested-nav-item.collapsed-item .nav-icon i {
  font-size: 0.9rem !important;
}

.nested-nav-item.collapsed-item:hover {
  background: rgba(250, 245, 255, 0.9) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(196, 181, 253, 0.1) !important;
  border-color: rgba(196, 181, 253, 0.3) !important;
}

.nested-nav-item.collapsed-item:hover .nav-icon {
  color: #7c3aed !important;
}

.nested-nav-item.collapsed-item.active {
  background: rgba(139, 92, 246, 0.2) !important;
  border-color: rgba(139, 92, 246, 0.6) !important;
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3) !important;
}

.nested-nav-item.collapsed-item.active .nav-icon {
  color: #5b21b6 !important;
}


/* Main sidebar nav items */
.main-sidebar .nav-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.875rem;
  border-radius: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  background: rgba(255, 255, 255, 0.5);
  color: #475569;
  font-size: 0.875rem;
  font-weight: 500;
}

.main-sidebar .nav-item:hover {
  background: rgba(255, 255, 255, 0.8);
  border-color: rgba(85, 33, 190, 0.1);
  transform: translateX(2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  color: #1e293b;
}

.main-sidebar .nav-item.active {
  background: linear-gradient(135deg, rgba(85, 33, 190, 0.1) 0%, rgba(224, 54, 175, 0.05) 100%);
  border-color: rgba(85, 33, 190, 0.2);
  box-shadow: 0 2px 8px rgba(85, 33, 190, 0.15);
  border-left: 3px solid #5521be;
  color: #5521be;
}

.main-sidebar.collapsed .nav-item {
  justify-content: center;
  padding: 0.75rem;
  background: transparent;
  border: none;
  box-shadow: none;
}

.main-sidebar.collapsed .nav-item:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: none;
  box-shadow: none;
}

.main-sidebar.collapsed .nav-item.active {
  background: transparent;
  border: none;
  box-shadow: none;
}

/* Main sidebar nav icon */
.main-sidebar .nav-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.8);
  flex-shrink: 0;
  color: #64748b;
  transition: all 0.2s ease;
}

.main-sidebar .nav-icon i {
  font-size: 1rem;
}

/* Active icon styling for main sidebar */
.main-sidebar .nav-icon.active-icon {
  background: linear-gradient(135deg, #5521be 0%, #e036af 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(85, 33, 190, 0.3);
}

/* When main sidebar collapsed, make active icon more prominent */
.main-sidebar.collapsed .nav-icon.active-icon {
  background: linear-gradient(135deg, #5521be 0%, #e036af 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(85, 33, 190, 0.4);
  transform: scale(1.1);
}

/* Hover effect for icons when main sidebar collapsed */
.main-sidebar.collapsed .nav-item:hover .nav-icon:not(.active-icon) {
  background: rgba(255, 255, 255, 0.9);
  color: #5521be;
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(85, 33, 190, 0.2);
}

/* Keep active icon unchanged on hover in main sidebar */
.main-sidebar.collapsed .nav-item:hover .nav-icon.active-icon {
  transform: scale(1.1);
}

/* Nav Name */
.nav-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: inherit;
  white-space: nowrap;
  overflow: hidden;
  flex: 1;
  text-overflow: ellipsis;
}

/* Nested indicator for parent features */
.nested-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1rem;
  height: 1rem;
  opacity: 0.5;
  transition: all 0.2s ease;
  font-size: 0.75rem;
}

.nav-item.has-nested:hover .nested-indicator {
  opacity: 0.8;
  transform: translateX(2px);
}

.nav-item.has-nested.active .nested-indicator {
  opacity: 1;
  color: #5521be;
}

/* Enhanced hover effect for collapsed sidebar */
.sidebar.collapsed .nav-item {
  position: relative;
}

.sidebar.collapsed .nav-item:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  border-radius: 12px !important;
}

/* User Section */
.user-section {
  padding: 1rem 0.75rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: auto;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #5521be 0%, #e036af 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  flex-shrink: 0;
}

.user-details {
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.user-email {
  font-size: 0.75rem;
  color: #64748b;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
}

.user-actions {
  display: flex;
  justify-content: center;
}

.sidebar.collapsed .user-actions {
  justify-content: center;
}

.logout-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 0.5rem;
  color: #dc2626;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 100%;
  justify-content: center;
}

.logout-button:hover {
  background: rgba(239, 68, 68, 0.15);
  border-color: rgba(239, 68, 68, 0.3);
  transform: translateY(-1px);
}

.logout-button i {
  font-size: 0.875rem;
}

.logout-text {
  font-weight: 500;
}

.sidebar.collapsed .logout-button {
  padding: 0.5rem;
  width: auto;
}

/* Mobile Overlay */
.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 998;
  backdrop-filter: blur(4px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .sidebar {
    position: fixed !important;
    top: 0;
    left: 0;
    height: 100vh;
    width: 280px !important;
    z-index: 999;
    transform: translateX(-100%);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 2px 0 20px rgba(0, 0, 0, 0.15);
  }

  .sidebar.mobile-open {
    transform: translateX(0) !important;
  }

  .sidebar.collapsed {
    transform: translateX(-100%) !important;
    width: 280px !important;
  }

  .sidebar.collapsed.mobile-open {
    transform: translateX(0) !important;
    width: 280px !important;
  }

  /* Hide desktop toggle button on mobile */
  .sidebar .toggle-btn {
    display: none !important;
  }

  /* Hide nested sidebar on mobile - use main sidebar for all navigation */
  .nested-sidebar-container {
    display: none !important;
  }
}

/* Unified Navigation Styles - all nested items use the same settings-based style */

/* No visual separation - all items look the same */
.nested-nav-item:first-child {
  margin-bottom: 0;
}

/* Dashboard item uses same styling as other nested items */

/* Enhanced animations for nested sidebar */
@keyframes slideInRight {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Smooth transitions for nested items */
.nested-nav-item {
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.nested-nav-item:hover {
  transition: all 0.15s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}
