import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';
import { CheckboxModule } from 'primeng/checkbox';
import { InputTextModule } from 'primeng/inputtext';
import { TagModule } from 'primeng/tag';
import { DividerModule } from 'primeng/divider';
import { CardModule } from 'primeng/card';
import {
  AccountKeywordMapping,
  CreativesUploaderConfigDialogData,
  CreativesUploaderConfigResult,
  CreativesUploaderExternalConfig,
} from '../models';

@Component({
  selector: 'app-creatives-uploader-config-dialog',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ButtonModule,
    DialogModule,
    CheckboxModule,
    InputTextModule,
    TagModule,
    DividerModule,
    CardModule,
  ],
  template: `
    <p-dialog
      [visible]="visible"
      [modal]="true"
      [closable]="false"
      [draggable]="false"
      [resizable]="false"
      [style]="{ width: '900px', maxHeight: '80vh' }"
      styleClass="creatives-uploader-config-dialog"
      header="⚙️ Configure Account Keywords"
    >
      <div class="dialog-content" *ngIf="dialogData">
        <!-- Account Selection -->
        <div class="config-section">
          <div class="section-header">
            <h3 class="section-title">
              <i class="pi pi-list"></i>
              Configure Facebook Accounts & Keywords
            </h3>
            <p-button
              label="Select All"
              icon="pi pi-check-square"
              [text]="true"
              size="small"
              (onClick)="selectAllAccounts()"
            >
            </p-button>
          </div>

          <p class="section-description">
            For each selected account, specify a keyword that will be used to
            search Google Drive files by description.
          </p>

          <div
            class="accounts-grid"
            *ngIf="dialogData.availableAccounts.length > 0"
          >
            <div
              *ngFor="let account of dialogData.availableAccounts"
              class="account-card"
            >
              <p-card>
                <div class="account-content">
                  <div class="account-header">
                    <p-checkbox
                      [binary]="true"
                      [(ngModel)]="selectedAccounts[account.ad_account_id]"
                      [inputId]="'account-' + account.ad_account_id"
                      (onChange)="
                        onAccountSelectionChange(account.ad_account_id)
                      "
                    >
                    </p-checkbox>
                    <div class="account-info">
                      <div class="account-name">
                        {{ account.ad_account_name }}
                      </div>
                      <div class="account-id">{{ account.ad_account_id }}</div>
                    </div>
                  </div>

                  <div class="account-meta">
                    <p-tag
                      [value]="getAccountStatusLabel(account.account_status)"
                      [severity]="
                        getAccountStatusSeverity(account.account_status)
                      "
                    >
                    </p-tag>
                    <span class="account-currency">{{ account.currency }}</span>
                  </div>

                  <div class="account-details">
                    <div class="detail-item">
                      <i class="fab fa-facebook-f"></i>
                      <span>{{ account.app_name }}</span>
                    </div>
                    <div class="detail-item">
                      <i class="pi pi-user"></i>
                      <span>{{ account.user_name }}</span>
                    </div>
                  </div>

                  <!-- Keyword Input -->
                  <div
                    class="keyword-section"
                    *ngIf="selectedAccounts[account.ad_account_id]"
                  >
                    <label
                      [for]="'keyword-' + account.ad_account_id"
                      class="keyword-label"
                    >
                      <i class="pi pi-search"></i>
                      Description Keyword
                    </label>
                    <input
                      pInputText
                      [id]="'keyword-' + account.ad_account_id"
                      [(ngModel)]="accountKeywords[account.ad_account_id]"
                      (input)="onKeywordChange()"
                      placeholder="Enter keyword for Google Drive search"
                      class="keyword-input"
                      [class.error]="hasKeywordError(account.ad_account_id)"
                    />
                    <small class="keyword-help">
                      Files with descriptions containing this keyword will be
                      uploaded to this account
                    </small>
                    <div
                      class="keyword-errors"
                      *ngIf="hasKeywordError(account.ad_account_id)"
                    >
                      <small
                        class="error-message"
                        *ngIf="isKeywordBlank(account.ad_account_id)"
                      >
                        <i class="pi pi-exclamation-triangle"></i>
                        Keyword cannot be empty
                      </small>
                      <small
                        class="error-message"
                        *ngIf="isKeywordDuplicate(account.ad_account_id)"
                      >
                        <i class="pi pi-exclamation-triangle"></i>
                        This keyword is already used by another account
                      </small>
                    </div>
                  </div>
                </div>
              </p-card>
            </div>
          </div>

          <div
            class="no-accounts"
            *ngIf="dialogData.availableAccounts.length === 0"
          >
            <i class="pi pi-info-circle"></i>
            <p>
              No Facebook accounts available. Please configure accounts in
              Facebook Accounts Manager first.
            </p>
          </div>
        </div>
      </div>

      <ng-template pTemplate="footer">
        <div class="dialog-footer">
          <p-button
            label="Cancel"
            icon="pi pi-times"
            [text]="true"
            (onClick)="onCancel()"
          >
          </p-button>
          <p-button
            label="Save Configuration"
            icon="pi pi-check"
            (onClick)="onSave()"
            [disabled]="!isValidConfiguration()"
          >
          </p-button>
        </div>
      </ng-template>
    </p-dialog>
  `,
  styles: [
    `
      .dialog-content {
        padding: 1rem 0;
      }

      .config-section {
        margin-bottom: 2rem;
      }

      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;
      }

      .section-title {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 1.1rem;
        font-weight: 600;
        color: #5521be;
        margin: 0;
      }

      .section-description {
        margin-bottom: 1.5rem;
        color: #6b7280;
        font-size: 0.9rem;
        line-height: 1.5;
      }

      .accounts-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
        gap: 1rem;
        max-height: 500px;
        overflow-y: auto;
        padding: 0.5rem;
      }

      .account-card {
        transition: transform 0.2s ease;
      }

      .account-card:hover {
        transform: translateY(-2px);
      }

      .account-content {
        padding: 0.5rem;
      }

      .account-header {
        display: flex;
        align-items: flex-start;
        gap: 0.75rem;
        margin-bottom: 0.75rem;
      }

      .account-info {
        flex: 1;
      }

      .account-name {
        font-weight: 600;
        margin-bottom: 0.25rem;
        color: #1f2937;
      }

      .account-id {
        font-size: 0.875rem;
        color: #6b7280;
        font-family: monospace;
      }

      .account-meta {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.75rem;
      }

      .account-currency {
        font-size: 0.875rem;
        color: #6b7280;
        font-weight: 500;
      }

      .account-details {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
        margin-bottom: 1rem;
      }

      .detail-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
        color: #6b7280;
      }

      .detail-item i {
        width: 16px;
        text-align: center;
      }

      .keyword-section {
        border-top: 1px solid #e5e7eb;
        padding-top: 1rem;
        margin-top: 1rem;
      }

      .keyword-label {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-weight: 600;
        color: #374151;
        margin-bottom: 0.5rem;
        font-size: 0.875rem;
      }

      .keyword-input {
        width: 100%;
        margin-bottom: 0.5rem;
      }

      .keyword-help {
        color: #6b7280;
        font-size: 0.75rem;
        line-height: 1.4;
      }

      .keyword-errors {
        margin-top: 0.5rem;
      }

      .error-message {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        color: #dc2626;
        font-size: 0.75rem;
        line-height: 1.4;
        margin-bottom: 0.25rem;
      }

      .error-message i {
        font-size: 0.75rem;
      }

      .no-accounts {
        text-align: center;
        padding: 2rem;
        color: #6b7280;
      }

      .no-accounts i {
        font-size: 2rem;
        margin-bottom: 1rem;
        color: #9ca3af;
      }

      .dialog-footer {
        display: flex;
        justify-content: flex-end;
        gap: 0.75rem;
        padding-top: 1rem;
      }

      :host ::ng-deep .p-dialog .p-dialog-header {
        background: linear-gradient(135deg, #5521be 0%, #e036af 100%);
        color: white;
      }

      :host ::ng-deep .p-dialog .p-dialog-header .p-dialog-title {
        font-weight: 600;
      }

      :host ::ng-deep .p-card {
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        border: 1px solid #e5e7eb;
      }

      :host ::ng-deep .p-card:hover {
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      }

      :host ::ng-deep .p-tag {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
      }

      :host ::ng-deep .p-divider {
        margin: 1.5rem 0;
      }

      :host ::ng-deep .p-inputtext {
        border-radius: 6px;
        border: 1px solid #d1d5db;
        padding: 0.5rem 0.75rem;
      }

      :host ::ng-deep .p-inputtext:focus {
        border-color: #5521be;
        box-shadow: 0 0 0 2px rgba(85, 33, 190, 0.1);
      }

      :host ::ng-deep .p-inputtext.error {
        border-color: #dc2626;
        background-color: #fef2f2;
      }

      :host ::ng-deep .p-inputtext.error:focus {
        border-color: #dc2626;
        box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.1);
      }
    `,
  ],
})
export class CreativesUploaderConfigDialogComponent implements OnInit {
  @Input() visible = false;
  @Output() result = new EventEmitter<CreativesUploaderConfigResult>();
  selectedAccounts: { [accountId: string]: boolean } = {};
  accountKeywords: { [accountId: string]: string } = {};
  removedAccountIds: string[] = [];

  private _dialogData: CreativesUploaderConfigDialogData | null = null;

  get dialogData(): CreativesUploaderConfigDialogData | null {
    return this._dialogData;
  }

  @Input() set dialogData(data: CreativesUploaderConfigDialogData | null) {
    this._dialogData = data;
    this.initializeFromConfig();
  }

  ngOnInit(): void {
    // Initialization is handled in the dialogData setter
  }

  getAccountStatusLabel(status: number): string {
    const statusMap: { [key: number]: string } = {
      1: 'Active',
      2: 'Disabled',
      3: 'Unsettled',
      7: 'Pending Review',
      8: 'Pending Settlement',
      9: 'In Grace Period',
      101: 'Temporarily Unavailable',
      102: 'Pending Closure',
    };
    return statusMap[status] || 'Unknown';
  }

  getAccountStatusSeverity(
    status: number,
  ): 'success' | 'info' | 'warning' | 'danger' {
    if (status === 1) return 'success';
    if ([7, 8, 9].includes(status)) return 'warning';
    if ([2, 101, 102].includes(status)) return 'danger';
    return 'info';
  }

  selectAllAccounts(): void {
    if (this._dialogData?.availableAccounts) {
      this._dialogData.availableAccounts.forEach((account) => {
        this.selectedAccounts[account.ad_account_id] = true;
        // Initialize with empty keyword if not already set
        if (!this.accountKeywords[account.ad_account_id]) {
          this.accountKeywords[account.ad_account_id] = '';
        }
      });
    }
  }

  onAccountSelectionChange(accountId: string): void {
    if (!this.selectedAccounts[accountId]) {
      // If unchecked, clear the keyword
      delete this.accountKeywords[accountId];
    } else {
      // If checked, initialize with empty keyword
      if (!this.accountKeywords[accountId]) {
        this.accountKeywords[accountId] = '';
      }
    }
  }

  onKeywordChange(): void {
    // Trigger validation when keywords change
    // This will update the UI to show/hide error messages
  }

  isKeywordBlank(accountId: string): boolean {
    const keyword = this.accountKeywords[accountId];
    return !keyword || keyword.trim().length === 0;
  }

  isKeywordDuplicate(accountId: string): boolean {
    const keyword = this.accountKeywords[accountId];
    if (!keyword || keyword.trim().length === 0) {
      return false; // Don't show duplicate error for blank keywords
    }

    const trimmedKeyword = keyword.trim().toLowerCase();
    const selectedAccountIds = Object.keys(this.selectedAccounts).filter(
      (id) => this.selectedAccounts[id],
    );

    // Check if any other selected account has the same keyword
    return selectedAccountIds.some((otherAccountId) => {
      if (otherAccountId === accountId) return false; // Don't compare with itself
      const otherKeyword = this.accountKeywords[otherAccountId];
      return (
        otherKeyword && otherKeyword.trim().toLowerCase() === trimmedKeyword
      );
    });
  }

  hasKeywordError(accountId: string): boolean {
    return this.isKeywordBlank(accountId) || this.isKeywordDuplicate(accountId);
  }

  isValidConfiguration(): boolean {
    const selectedAccountIds = Object.keys(this.selectedAccounts).filter(
      (id) => this.selectedAccounts[id],
    );

    // If no accounts selected, configuration is valid (can save empty config)
    if (selectedAccountIds.length === 0) {
      return true;
    }

    // If accounts are selected, check that all have valid keywords (not blank and not duplicate)
    return selectedAccountIds.every(
      (accountId) => !this.hasKeywordError(accountId),
    );
  }

  onSave(): void {
    const selectedAccountIds = Object.keys(this.selectedAccounts).filter(
      (id) => this.selectedAccounts[id],
    );

    const accountKeywords: AccountKeywordMapping[] = selectedAccountIds
      .filter((accountId) => !this.hasKeywordError(accountId)) // Extra safety check
      .map((accountId) => ({
        ad_account_id: accountId,
        description_keyword: this.accountKeywords[accountId].trim(),
      }));

    const config: CreativesUploaderExternalConfig = {
      accountKeywords,
    };

    this.result.emit({
      config,
      confirmed: true,
    });
  }

  onCancel(): void {
    this.result.emit({
      config: {} as CreativesUploaderExternalConfig,
      confirmed: false,
    });
  }

  private initializeFromConfig(): void {
    // Reset selected accounts, keywords, and removed accounts
    this.selectedAccounts = {};
    this.accountKeywords = {};
    this.removedAccountIds = [];

    if (this._dialogData?.currentConfig && this._dialogData?.availableAccounts) {
      const config = this._dialogData.currentConfig;
      const availableAccountIds = new Set(
        this._dialogData.availableAccounts.map(acc => acc.ad_account_id)
      );

      // Initialize from saved config, but only for accounts that still exist
      if (config.accountKeywords) {
        const validMappings: any[] = [];
        const invalidAccountIds: string[] = [];

        config.accountKeywords.forEach((mapping) => {
          if (availableAccountIds.has(mapping.ad_account_id)) {
            this.selectedAccounts[mapping.ad_account_id] = true;
            this.accountKeywords[mapping.ad_account_id] = mapping.description_keyword;
            validMappings.push(mapping);
          } else {
            invalidAccountIds.push(mapping.ad_account_id);
          }
        });

        // Store removed account IDs for display
        this.removedAccountIds = invalidAccountIds;

        // Log information about invalid accounts for debugging
        if (invalidAccountIds.length > 0) {
          console.warn(
            `Creatives Uploader configuration contains ${invalidAccountIds.length} account(s) that no longer exist:`,
            invalidAccountIds
          );
        }
      }
    }
  }
}
