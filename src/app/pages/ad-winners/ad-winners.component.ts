import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';
// PrimeNG imports
import { Table, TableModule } from 'primeng/table';
import { SelectModule } from 'primeng/select';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { CardModule } from 'primeng/card';
import { TagModule } from 'primeng/tag';
import { ProgressBarModule } from 'primeng/progressbar';
import { TooltipModule } from 'primeng/tooltip';
import { ImageModule } from 'primeng/image';
import { SkeletonModule } from 'primeng/skeleton';
import { CalendarModule } from 'primeng/calendar';
import { InputNumberModule } from 'primeng/inputnumber';
import { DialogModule } from 'primeng/dialog';
import { ToastModule } from 'primeng/toast';
import { ContextMenuModule } from 'primeng/contextmenu';
import { MenuItem, MessageService } from 'primeng/api';

import { AdWinnersConfigService, AdWinnerService } from './services';
import { AdIteratorService } from './pages/ad-iterator/services';
import {
  AD_WINNER_TABLE_COLUMNS,
  AdWinner,
  AdWinnerFilters,
  AdWinnerResponse,
  AdWinnersConfigDialogData,
  AdWinnersConfigResult,
  AdWinnerTableColumn,
  ALL_AVAILABLE_METRICS,
  FilterOption,
  PerformanceScoreConfiguration,
  PeriodOption,
  WeeklyPeriod,
} from './models';
import {
  MediaModalComponent,
  MetricFormulaComponent,
  PerformanceScoreBuilderComponent,
} from './components';
import { AdWinnersConfigDialogComponent } from './components/ad-winners-config-dialog.component';
import { FeatureHeaderComponent } from '../../shared';
import { PerformanceScoreConfigService } from './services/performance-score-config.service';

@Component({
  selector: 'chm-ad-winners',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TableModule,
    SelectModule,
    ButtonModule,
    InputTextModule,
    CardModule,
    TagModule,
    ProgressBarModule,
    TooltipModule,
    ImageModule,
    SkeletonModule,
    CalendarModule,
    InputNumberModule,
    DialogModule,
    ToastModule,
    ContextMenuModule,
    MetricFormulaComponent,
    PerformanceScoreBuilderComponent,
    MediaModalComponent,
    AdWinnersConfigDialogComponent,
    FeatureHeaderComponent,
  ],
  providers: [MessageService],
  templateUrl: './ad-winners.component.html',
  styleUrls: ['./ad-winners.component.css'],
})
export class AdWinnersComponent implements OnInit, OnDestroy {
  @ViewChild('dt') dt!: Table;
  @ViewChild('rowContextMenu') rowContextMenu!: any;
  // Data
  adWinners: AdWinner[] = [];
  filteredAdWinners: AdWinner[] = [];
  loading = false;
  totalRecords = 0;
  searchTerm = '';
  // Filter options
  accounts: FilterOption[] = [];
  campaigns: FilterOption[] = [];
  adSets: FilterOption[] = [];
  weeklyPeriods: WeeklyPeriod[] = [];
  periodOptions: PeriodOption[] = [];
  selectedPeriod: string = '';
  // metrics removed - now using Performance Score Configuration
  // Current filters
  filters: AdWinnerFilters = {};
  // Table configuration
  columns: AdWinnerTableColumn[] = AD_WINNER_TABLE_COLUMNS;
  // View mode
  viewMode: 'table' | 'cards' = 'table';
  // Row actions
  selectedAdWinner: AdWinner | null = null;
  rowMenuItems: MenuItem[] = [];

  // Image/Video modal
  showImageModal = false;
  selectedImage: {
    url: string;
    alt: string;
    adName: string;
    adId: string;
    creativeType: string;
    isVideo?: boolean;
    videoSource?: string;
  } | null = null;

  // Performance Score Builder
  showScoreBuilder = false;
  currentScoreConfiguration: PerformanceScoreConfiguration | null = null;

  // Configuration Dialog
  showConfigDialog = false;
  configDialogData: AdWinnersConfigDialogData | null = null;

  private destroy$ = new Subject<void>();

  constructor(
    private adWinnerService: AdWinnerService,
    private configService: PerformanceScoreConfigService,
    private adWinnersConfigService: AdWinnersConfigService,
    private messageService: MessageService,
  ) {}

  ngOnInit(): void {
    this.loadDefaultConfiguration();
    this.loadInitialData();
  }

  loadDefaultConfiguration(): void {
    this.configService.getDefaultConfiguration().subscribe((config) => {
      if (config) {
        this.currentScoreConfiguration = config;
        console.log('Loaded default configuration:', config.name);
      } else {
        console.log('No default configuration found');
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onRowClick(adWinner: AdWinner, event: Event): void {
    // Prevent default if clicking on interactive elements
    const target = event.target as HTMLElement;
    if (
      target.tagName === 'VIDEO' ||
      target.tagName === 'IMG' ||
      target.closest('.video-thumbnail-wrapper')
    ) {
      return; // Let the existing click handlers work
    }

    this.selectedAdWinner = adWinner;
    this.showRowActions(event);
  }

  onRowRightClick(adWinner: AdWinner, event: Event): void {
    event.preventDefault();
    this.selectedAdWinner = adWinner;
    this.setupRowMenuItems();
    this.rowContextMenu.show(event);
  }

  loadAdWinners(): void {
    this.loading = true;

    this.adWinnerService
      .getAdWinners(this.filters)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response: AdWinnerResponse) => {
          // Add computed running time days and recalculate performance scores
          this.adWinners = response.data.map((ad) => {
            let performanceScore = ad.performance_score;

            // Recalculate performance score if we have a custom configuration
            if (this.currentScoreConfiguration && ad.weekly_insights) {
              performanceScore =
                this.adWinnerService.calculatePerformanceScoreWithConfig(
                  ad.weekly_insights,
                  this.currentScoreConfiguration,
                );
            }

            return {
              ...ad,
              performance_score: performanceScore,
              runningTimeDays: this.getRunningTimeDays(ad.created_time),
            };
          });

          this.filteredAdWinners = this.adWinners;
          this.totalRecords = response.total_count;
          this.loading = false;
          this.applySearch(); // Apply current search if any
        },
        error: (error) => {
          console.error('Error loading ad winners:', error);
          this.loading = false;
        },
      });
  }

  onAccountChange(): void {
    // Reset dependent filters
    this.filters.campaign_id = undefined;
    this.filters.adset_id = undefined;
    this.campaigns = [];
    this.adSets = [];

    if (this.filters.account_id) {
      // Load campaigns for selected account
      this.adWinnerService
        .getCampaigns(this.filters.account_id)
        .pipe(takeUntil(this.destroy$))
        .subscribe((campaigns) => {
          this.campaigns = campaigns;
        });
    }

    this.loadAdWinners();
  }

  onCampaignChange(): void {
    // Reset dependent filters
    this.filters.adset_id = undefined;
    this.adSets = [];

    if (this.filters.campaign_id) {
      // Load ad sets for selected campaign
      this.adWinnerService
        .getAdSets(this.filters.campaign_id)
        .pipe(takeUntil(this.destroy$))
        .subscribe((adSets) => {
          this.adSets = adSets;
        });
    }

    this.loadAdWinners();
  }

  onAdSetChange(): void {
    this.loadAdWinners();
  }

  onPeriodChange(): void {
    // Find the selected period option and update filters
    const selectedOption = this.periodOptions.find(
      (option) => option.value === this.selectedPeriod,
    );
    if (selectedOption) {
      this.filters.week_start = selectedOption.week_start;
      this.filters.week_end = selectedOption.week_end;
    }
    this.loadAdWinners();
  }

  getPeriodLabelOnly(fullLabel: string): string {
    // Extract just the period part (after the newline)
    const parts = fullLabel.split('\n');
    return parts.length > 1 ? parts[1] : parts[0];
  }

  onFiltersChange(): void {
    this.loadAdWinners();
  }

  clearFilters(): void {
    this.filters = {};
    this.campaigns = [];
    this.adSets = [];

    // Set default period if available
    if (this.weeklyPeriods.length > 0) {
      this.filters.week_start = this.weeklyPeriods[0].week_start;
      this.filters.week_end = this.weeklyPeriods[0].week_end;
    }

    this.loadAdWinners();
  }

  // Utility methods for template
  formatCurrency(value: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(value);
  }

  formatPercentage(value: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'percent',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  }

  formatNumber(value: number): string {
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  }

  formatRunningTime(createdTime: string): string {
    if (!createdTime) return 'N/A';

    const created = new Date(createdTime);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - created.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return `${diffDays}D`;
  }

  getRunningTimeDays(createdTime: string): number {
    if (!createdTime) return 0;

    const created = new Date(createdTime);
    const now = new Date();

    // Check if date is valid
    if (isNaN(created.getTime())) {
      return 0;
    }

    const diffTime = Math.abs(now.getTime() - created.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return diffDays;
  }

  getPerformanceColor(score: number): string {
    if (score >= 80) return 'success';
    if (score >= 60) return 'warning';
    return 'danger';
  }

  getRankColor(rank: number): string {
    if (rank === 1) return 'success';
    if (rank <= 3) return 'warning';
    return 'secondary';
  }

  getImageUrl(creative: any): string {
    if (!creative) {
      return '/images/placeholder-ad.svg';
    }

    // Check direct image/thumbnail URLs first
    if (creative.image_url) {
      return creative.image_url;
    }

    if (creative.thumbnail_url) {
      return creative.thumbnail_url;
    }

    // Check object_story_spec for different creative types
    if (creative.object_story_spec) {
      const spec = creative.object_story_spec;

      // Video data
      if (spec.video_data?.image_url) {
        return spec.video_data.image_url;
      }

      // Photo data
      if (spec.photo_data?.image_url) {
        return spec.photo_data.image_url;
      }

      // Link data
      if (spec.link_data?.image_url) {
        return spec.link_data.image_url;
      }

      if (spec.link_data?.picture) {
        return spec.link_data.picture;
      }
    }

    return '/images/placeholder-ad.svg';
  }

  hasImage(creative: any): boolean {
    if (!creative) {
      return false;
    }

    // Check direct URLs
    if (creative.image_url || creative.thumbnail_url) {
      return true;
    }

    // Check object_story_spec
    if (creative.object_story_spec) {
      const spec = creative.object_story_spec;

      return !!(
        spec.video_data?.image_url ||
        spec.photo_data?.image_url ||
        spec.link_data?.image_url ||
        spec.link_data?.picture
      );
    }

    return false;
  }

  hasVideo(ad: AdWinner): boolean {
    return !!(
      (ad.video_id && ad.video?.source) ||
      ad.creative?.video_url ||
      ad.creative?.object_story_spec?.video_data?.video_url
    );
  }

  // Check if we have an available video source (not null)
  hasAvailableVideo(ad: AdWinner): boolean {
    return !!(
      (ad.video_id && ad.video?.source) ||
      ad.creative?.video_url ||
      ad.creative?.object_story_spec?.video_data?.video_url
    );
  }

  // Check if ad has video_id but source is null (video not yet updated)
  hasVideoButNotAvailable(ad: AdWinner): boolean {
    return !!(ad.video_id && !ad.video?.source);
  }

  getVideoUrl(ad: AdWinner): string | undefined {
    // First check if we have a video from the videos table
    if (ad.video_id && ad.video?.source) {
      return ad.video.source;
    }

    // Fallback to creative video URLs
    if (ad.creative?.video_url) {
      return ad.creative.video_url;
    }

    if (ad.creative?.object_story_spec?.video_data?.video_url) {
      return ad.creative.object_story_spec.video_data.video_url;
    }

    return undefined;
  }

  getCreativeType(creative: any): string {
    if (!creative?.object_story_spec) {
      return 'unknown';
    }

    const spec = creative.object_story_spec;

    if (spec.video_data) {
      return 'video';
    }

    if (spec.photo_data) {
      return 'photo';
    }

    if (spec.link_data) {
      return 'link';
    }

    if (spec.template_data) {
      return 'template';
    }

    return 'unknown';
  }

  getTotalSpend(): string {
    const total = this.adWinners.reduce((sum, ad) => {
      return sum + (ad.weekly_insights?.total_spend || 0);
    }, 0);
    return this.formatCurrency(total);
  }

  getRankIcon(rank: number): string {
    if (rank === 1) return '🥇';
    if (rank <= 3) return '🥈';
    return '🥉';
  }

  onImageError(event: any): void {
    event.target.src = '/images/placeholder-ad.svg';
  }

  toggleViewMode(): void {
    // Save current search term before switching
    const currentSearchTerm = this.searchTerm;

    this.viewMode = this.viewMode === 'table' ? 'cards' : 'table';

    // Restore search term after view change
    setTimeout(() => {
      if (currentSearchTerm) {
        this.searchTerm = currentSearchTerm;
        this.applySearch();

        // If switching to table view, also apply to table
        if (this.viewMode === 'table' && this.dt) {
          this.dt.filterGlobal(currentSearchTerm, 'contains');
        }
      }
    }, 0);
  }

  getBestMetricLabel(bestMetric: string): string {
    const metric = ALL_AVAILABLE_METRICS.find((m) => m.metric === bestMetric);
    return metric?.label || bestMetric;
  }

  onGlobalFilter(value: string, table?: Table): void {
    this.searchTerm = value;

    // Apply filter to table if provided
    if (table) {
      table.filterGlobal(value, 'contains');
    }

    // Apply filter to cards view
    this.applySearch();
  }

  openImageModal(adWinner: AdWinner): void {
    const hasAvailableVideo = this.hasAvailableVideo(adWinner);
    const hasImage = this.hasImage(adWinner.creative);

    if (hasAvailableVideo || hasImage) {
      this.selectedImage = {
        url: this.getImageUrl(adWinner.creative),
        alt: adWinner.name,
        adName: adWinner.name,
        adId: adWinner.id,
        creativeType: this.getCreativeType(adWinner.creative),
        isVideo: hasAvailableVideo,
        videoSource: hasAvailableVideo ? this.getVideoUrl(adWinner) : undefined,
      };
      this.showImageModal = true;
    }
  }

  closeImageModal(): void {
    this.showImageModal = false;
    this.selectedImage = null;
  }

  onVideoUrlRefreshed(newVideoUrl: string): void {
    console.log('🔄 Video URL refreshed:', newVideoUrl);

    // Update the selectedImage with the new video URL
    if (this.selectedImage && newVideoUrl) {
      this.selectedImage.videoSource = newVideoUrl;
      console.log('✅ Updated selectedImage with new video URL');
    }
  }

  // Performance Score Builder Methods
  openScoreBuilder(): void {
    this.showScoreBuilder = true;
  }

  onScoreConfigurationSaved(config: PerformanceScoreConfiguration): void {
    console.log('Configuration saved:', config);
    // Configuration is already saved to Supabase by the service
    // You could show a toast notification here
  }

  onScoreConfigurationApplied(config: PerformanceScoreConfiguration): void {
    this.currentScoreConfiguration = config;

    // Apply the new configuration and reload data
    console.log('Configuration applied:', config);
    this.loadAdWinners();

    // You could show a toast notification here
  }

  // Performance Score Configuration Methods
  getCurrentConfigName(): string {
    return this.currentScoreConfiguration?.name || 'Default E-commerce';
  }

  getPrimaryMetricName(): string {
    if (!this.currentScoreConfiguration) {
      return 'ROAS'; // Default primary metric
    }

    const sortedMetrics = this.currentScoreConfiguration.metrics_config.metrics
      .filter((m) => m.weight > 0)
      .sort((a, b) => b.weight - a.weight);

    return sortedMetrics.length > 0 ? sortedMetrics[0].label : 'ROAS';
  }

  getActiveMetricsCount(): number {
    if (!this.currentScoreConfiguration) {
      return 4; // Default count
    }

    return this.currentScoreConfiguration.metrics_config.metrics.filter(
      (m) => m.weight > 0,
    ).length;
  }

  /**
   * Open configuration dialog
   */
  openConfigDialog(): void {
    this.adWinnersConfigService
      .getConfigDialogData()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (data) => {
          this.configDialogData = data;
          this.showConfigDialog = true;
        },
        error: (error) => {
          console.error('Error loading config dialog data:', error);
        },
      });
  }

  /**
   * Handle configuration dialog result
   */
  onConfigResult(result: AdWinnersConfigResult): void {
    this.showConfigDialog = false;

    if (result.confirmed) {
      this.adWinnersConfigService
        .saveConfig(result.config)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: () => {
            const accountCount = result.config.selectedAccounts.length;
            const message =
              accountCount === 0
                ? 'Configuration cleared - no accounts selected for Ad Winners.'
                : `Successfully configured ${accountCount} Facebook account(s) for Ad Winners.`;

            this.messageService.add({
              severity: 'success',
              summary: 'Configuration Saved',
              detail: message,
              life: 5000,
            });
          },
          error: (error) => {
            console.error('Error saving configuration:', error);
            this.messageService.add({
              severity: 'error',
              summary: 'Configuration Error',
              detail: 'Failed to save configuration. Please try again.',
              life: 5000,
            });
          },
        });
    }
  }

  private showRowActions(event: Event): void {
    this.setupRowMenuItems();
    this.rowContextMenu.show(event);
  }

  private setupRowMenuItems(): void {
    if (!this.selectedAdWinner) return;

    this.rowMenuItems = [
      {
        label: 'Open in Facebook',
        icon: 'fab fa-facebook-f',
        command: () => this.openInFacebook(),
      },
      {
        label: 'Copy Ad ID',
        icon: 'pi pi-copy',
        command: () => this.copyAdId(),
      },
    ];
  }

  private openInFacebook(): void {
    if (!this.selectedAdWinner) return;

    const facebookUrl = `https://www.facebook.com/ads/library/?id=${this.selectedAdWinner.id}`;
    window.open(facebookUrl, '_blank');
  }

  private copyAdId(): void {
    if (!this.selectedAdWinner) return;

    navigator.clipboard
      .writeText(this.selectedAdWinner.id)
      .then(() => {
        this.messageService.add({
          severity: 'success',
          summary: 'Copied',
          detail: 'Ad ID copied to clipboard',
          life: 3000,
        });
      })
      .catch(() => {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: 'Failed to copy Ad ID',
          life: 3000,
        });
      });
  }

  private applySearch(): void {
    if (!this.searchTerm || this.searchTerm.trim() === '') {
      this.filteredAdWinners = [...this.adWinners];
      return;
    }

    const searchLower = this.searchTerm.toLowerCase().trim();
    this.filteredAdWinners = this.adWinners.filter((adWinner) => {
      return (
        adWinner.name?.toLowerCase().includes(searchLower) ||
        adWinner.adset?.name?.toLowerCase().includes(searchLower) ||
        adWinner.campaign?.name?.toLowerCase().includes(searchLower) ||
        adWinner.account?.name?.toLowerCase().includes(searchLower)
      );
    });
  }

  private loadInitialData(): void {
    // Load accounts
    this.adWinnerService
      .getAccounts()
      .pipe(takeUntil(this.destroy$))
      .subscribe((accounts) => {
        this.accounts = accounts;
      });

    // Load period options
    this.adWinnerService
      .getPeriodOptions()
      .pipe(takeUntil(this.destroy$))
      .subscribe((options) => {
        this.periodOptions = options;
        // Set default to 7D period if available, otherwise first available option
        if (options.length > 0) {
          const defaultOption =
            options.find((opt) => opt.value === '7d') || options[0];
          this.selectedPeriod = defaultOption.value;
          this.filters.week_start = defaultOption.week_start;
          this.filters.week_end = defaultOption.week_end;
          // Load data with default period
          this.loadAdWinners();
        }
      });

    // Load weekly periods (for backward compatibility)
    this.adWinnerService
      .getWeeklyPeriods()
      .pipe(takeUntil(this.destroy$))
      .subscribe((periods) => {
        this.weeklyPeriods = periods;
      });
  }
}
