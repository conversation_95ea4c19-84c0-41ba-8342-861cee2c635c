-- Ad Iterator Tables
-- Prefix: adw_adit_

-- Table 1: Ads in iterator
CREATE TABLE adw_adit_ads (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    ad_id TEXT NOT NULL,
    added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table 2: Tasks for ads
CREATE TABLE adw_adit_tasks (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    ad_id TEXT NOT NULL,
    task_type TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    input JSONB,
    output JSONB
);

-- Indexes for better performance
CREATE INDEX idx_adw_adit_ads_ad_id ON adw_adit_ads(ad_id);
CREATE INDEX idx_adw_adit_ads_added_at ON adw_adit_ads(added_at);

CREATE INDEX idx_adw_adit_tasks_ad_id ON adw_adit_tasks(ad_id);
CREATE INDEX idx_adw_adit_tasks_status ON adw_adit_tasks(status);
CREATE INDEX idx_adw_adit_tasks_task_type ON adw_adit_tasks(task_type);
CREATE INDEX idx_adw_adit_tasks_created_at ON adw_adit_tasks(created_at);

-- Add constraints
ALTER TABLE adw_adit_ads ADD CONSTRAINT unique_ad_id UNIQUE (ad_id);

ALTER TABLE adw_adit_tasks
ADD CONSTRAINT check_status
CHECK (status IN ('pending', 'running', 'completed', 'failed'));

-- RLS Policies for security
ALTER TABLE adw_adit_ads ENABLE ROW LEVEL SECURITY;
ALTER TABLE adw_adit_tasks ENABLE ROW LEVEL SECURITY;

-- Policy: Allow authenticated users to access all data
CREATE POLICY "Allow authenticated users to view ads" ON adw_adit_ads
    FOR ALL USING (auth.role() = 'authenticated');

CREATE POLICY "Allow authenticated users to manage tasks" ON adw_adit_tasks
    FOR ALL USING (auth.role() = 'authenticated');

-- Comments for documentation
COMMENT ON TABLE adw_adit_ads IS 'Stores Facebook ads that have been added to the ad iterator';
COMMENT ON COLUMN adw_adit_ads.ad_id IS 'Facebook Ad ID';
COMMENT ON COLUMN adw_adit_ads.added_at IS 'When the ad was added to iterator';

COMMENT ON TABLE adw_adit_tasks IS 'Tasks to be performed on ads in the iterator';
COMMENT ON COLUMN adw_adit_tasks.ad_id IS 'Reference to Facebook Ad ID';
COMMENT ON COLUMN adw_adit_tasks.task_type IS 'Free text describing the task type';
COMMENT ON COLUMN adw_adit_tasks.status IS 'Current status: pending, running, completed, failed';
COMMENT ON COLUMN adw_adit_tasks.input IS 'Input parameters for the task (JSON)';
COMMENT ON COLUMN adw_adit_tasks.output IS 'Task results and output data (JSON)';
