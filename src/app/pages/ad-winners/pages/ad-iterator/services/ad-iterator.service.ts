import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { SupabaseService } from '../../../../core/services/supabase.service';

export interface AdIteratorAd {
  id: string;
  ad_id: string;
  added_at: string;
}

export interface AdIteratorTask {
  id: string;
  ad_id: string;
  task_type: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  created_at: string;
  completed_at?: string;
  input?: any;
  output?: any;
}

@Injectable({
  providedIn: 'root'
})
export class AdIteratorService {

  constructor(private supabaseService: SupabaseService) {}

  /**
   * Add an ad to the iterator
   */
  addAdToIterator(adId: string): Observable<AdIteratorAd> {
    return this.supabaseService.from<AdIteratorAd>('adw_adit_ads')
      .insert({
        ad_id: adId
      })
      .select()
      .single();
  }

  /**
   * Remove an ad from the iterator
   */
  removeAdFromIterator(adId: string): Observable<void> {
    return this.supabaseService.from('adw_adit_ads')
      .delete()
      .eq('ad_id', adId)
      .then(() => void 0);
  }

  /**
   * Check if an ad is already in the iterator
   */
  isAdInIterator(adId: string): Observable<boolean> {
    return this.supabaseService.from('adw_adit_ads')
      .select('id')
      .eq('ad_id', adId)
      .single()
      .then(result => !!result.data)
      .catch(() => false);
  }

  /**
   * Get all ads in the iterator
   */
  getIteratorAds(): Observable<AdIteratorAd[]> {
    return this.supabaseService.from<AdIteratorAd>('adw_adit_ads')
      .select('*')
      .order('added_at', { ascending: false });
  }

  /**
   * Create a new task for an ad
   */
  createTask(adId: string, taskType: string, input?: any): Observable<AdIteratorTask> {
    return this.supabaseService.from<AdIteratorTask>('adw_adit_tasks')
      .insert({
        ad_id: adId,
        task_type: taskType,
        input: input
      })
      .select()
      .single();
  }

  /**
   * Update task status
   */
  updateTaskStatus(taskId: string, status: AdIteratorTask['status'], output?: any): Observable<AdIteratorTask> {
    const updateData: any = { 
      status,
      ...(status === 'completed' && { completed_at: new Date().toISOString() })
    };
    
    if (output) {
      updateData.output = output;
    }

    return this.supabaseService.from<AdIteratorTask>('adw_adit_tasks')
      .update(updateData)
      .eq('id', taskId)
      .select()
      .single();
  }

  /**
   * Get tasks for an ad
   */
  getAdTasks(adId: string): Observable<AdIteratorTask[]> {
    return this.supabaseService.from<AdIteratorTask>('adw_adit_tasks')
      .select('*')
      .eq('ad_id', adId)
      .order('created_at', { ascending: false });
  }

  /**
   * Get all tasks with optional status filter
   */
  getAllTasks(status?: AdIteratorTask['status']): Observable<AdIteratorTask[]> {
    let query = this.supabaseService.from<AdIteratorTask>('adw_adit_tasks')
      .select('*');
    
    if (status) {
      query = query.eq('status', status);
    }
    
    return query.order('created_at', { ascending: false });
  }

  /**
   * Delete a task
   */
  deleteTask(taskId: string): Observable<void> {
    return this.supabaseService.from('adw_adit_tasks')
      .delete()
      .eq('id', taskId)
      .then(() => void 0);
  }
}
