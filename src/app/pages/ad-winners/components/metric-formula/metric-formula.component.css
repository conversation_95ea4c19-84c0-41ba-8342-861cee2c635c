.metric-formula-container {
  max-width: 1000px;
  margin: 0 auto 2rem;
}

.formula-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(254, 236, 249, 0.4) 100%);
  border: 1px solid rgba(85, 33, 190, 0.1);
  border-radius: 0.5rem;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(10px);
}

.compact-content {
  padding: 1rem;
}

.header-section {
  margin-bottom: 0.75rem;
}

.title-row {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

.title-icon {
  color: #5521be;
  font-size: 1rem;
}

.title-text {
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  font-size: 0.9rem;
  color: #1e293b;
  flex: 1;
}

.primary-tag {
  font-size: 0.65rem;
  font-weight: 500;
  padding: 0.15rem 0.4rem;
}

.subtitle {
  font-family: 'Poppins', sans-serif;
  font-size: 0.75rem;
  color: #64748b;
  margin-left: 1.5rem;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  justify-items: stretch;
}


.metric-item {
  background: rgba(85, 33, 190, 0.02);
  border: 1px solid rgba(85, 33, 190, 0.08);
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  transition: all 0.15s ease;
  width: 100%;
  min-width: 0;
}

.metric-item:hover {
  border-color: rgba(85, 33, 190, 0.15);
  background: rgba(85, 33, 190, 0.04);
}

.metric-item.primary {
  background: linear-gradient(135deg, rgba(85, 33, 190, 0.08) 0%, rgba(224, 54, 175, 0.04) 100%);
  border-color: rgba(85, 33, 190, 0.2);
  box-shadow: 0 1px 3px rgba(85, 33, 190, 0.1);
}

.metric-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.25rem;
}

.metric-label {
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  font-size: 0.8rem;
  color: #1e293b;
}

.metric-weight {
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  font-size: 0.8rem;
  color: #5521be;
}

.metric-item.primary .metric-weight {
  color: #e036af;
  font-weight: 700;
}

.metric-bar {
  height: 4px;
  background: rgba(226, 232, 240, 0.6);
  border-radius: 2px;
  overflow: hidden;
}

.bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #94a3b8 0%, #cbd5e1 100%);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.bar-fill.primary-bar {
  background: linear-gradient(90deg, #5521be 0%, #e036af 100%);
}

.formula-summary {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: rgba(59, 130, 246, 0.05);
  border: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 0.375rem;
  font-family: 'Poppins', sans-serif;
  font-size: 0.75rem;
  font-weight: 500;
  color: #475569;
  margin-top: 2rem;
}

.formula-summary i {
  color: #3b82f6;
  font-size: 0.8rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .title-row {
    flex-wrap: wrap;
    gap: 0.25rem;
  }

  .compact-content {
    padding: 0.75rem;
  }

  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.4rem;
  }

  .metric-row {
    font-size: 0.75rem;
  }

  .metric-label {
    font-size: 0.7rem;
  }

  .metric-weight {
    font-size: 0.7rem;
  }
}

@media (max-width: 480px) {
  .metrics-grid {
    grid-template-columns: 1fr;
    gap: 0.3rem;
  }

  .metric-item {
    padding: 0.4rem 0.5rem;
  }
}

/* Animation for smooth transitions */
.formula-card {
  animation: fadeInUp 0.2s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
