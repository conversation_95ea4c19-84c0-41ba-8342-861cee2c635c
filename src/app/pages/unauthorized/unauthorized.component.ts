import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { ButtonModule } from 'primeng/button';

@Component({
  selector: 'app-unauthorized',
  standalone: true,
  imports: [
    CommonModule,
    ButtonModule
  ],
  template: `
    <div class="unauthorized-container">
      <div class="unauthorized-content">
        <div class="icon">
          <i class="pi pi-lock" style="font-size: 4rem; color: #ef4444;"></i>
        </div>
        <h1>Access Denied</h1>
        <p>You don't have permission to access this page.</p>
        <p-button 
          label="Go to Dashboard" 
          icon="pi pi-home"
          (onClick)="goToDashboard()">
        </p-button>
      </div>
    </div>
  `,
  styles: [`
    .unauthorized-container {
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 2rem;
    }
    
    .unauthorized-content {
      text-align: center;
      max-width: 400px;
    }
    
    .icon {
      margin-bottom: 2rem;
    }
    
    h1 {
      font-size: 2rem;
      font-weight: 700;
      color: #1e293b;
      margin: 0 0 1rem 0;
    }
    
    p {
      color: #64748b;
      margin-bottom: 2rem;
      font-size: 1.125rem;
    }
  `]
})
export class UnauthorizedComponent {
  constructor(private router: Router) {}
  
  goToDashboard() {
    this.router.navigate(['/dashboard']);
  }
}
