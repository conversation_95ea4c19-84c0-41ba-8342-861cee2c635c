import { PortalFeature } from './config.types';
import { AdWinnersConfig } from '../../pages/ad-winners/config/ad-winners.config';
import { FbAccountsManagerConfig } from '../../pages/fb-accounts-manager/config/fb-accounts-manager.config';
import { CreativesUploaderConfig } from '../../pages/creatives-uploader/config/creatives-uploader.config';

// Type-safe registry mapping each feature to its config type
export interface FeatureConfigRegistry {
  [PortalFeature.AD_WINNERS]: AdWinnersConfig;
  [PortalFeature.FB_ACCOUNTS_MANAGER]: FbAccountsManagerConfig;
  [PortalFeature.CREATIVES_UPLOADER]: CreativesUploaderConfig;
}

// Partial registry for customer overrides (allows partial configs)
export interface PartialFeatureConfigRegistry {
  [PortalFeature.AD_WINNERS]?: Partial<AdWinnersConfig>;
  [PortalFeature.FB_ACCOUNTS_MANAGER]?: Partial<FbAccountsManagerConfig>;
  [PortalFeature.CREATIVES_UPLOADER]?: Partial<CreativesUploaderConfig>;
}

// Helper type to get config type for a specific feature
export type FeatureConfigType<T extends PortalFeature> = FeatureConfigRegistry[T];

// Union type of all possible feature configs
export type AnyFeatureConfig = FeatureConfigRegistry[PortalFeature];
