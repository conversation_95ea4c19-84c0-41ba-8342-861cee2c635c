import { FeatureConfigRegistry, PortalFeature } from '../types';
import { defaultAdWinnersConfig } from '../../pages/ad-winners/config/ad-winners.config';
import { defaultFbAccountsManagerConfig } from '../../pages/fb-accounts-manager/config/fb-accounts-manager.config';
import { defaultCreativesUploaderConfig } from '../../pages/creatives-uploader/config/creatives-uploader.config';

// Type-safe default configurations for all features
export const defaultFeatureConfigs: FeatureConfigRegistry = {
  [PortalFeature.AD_WINNERS]: defaultAdWinnersConfig,
  [PortalFeature.FB_ACCOUNTS_MANAGER]: defaultFbAccountsManagerConfig,
  [PortalFeature.CREATIVES_UPLOADER]: defaultCreativesUploaderConfig,
};
