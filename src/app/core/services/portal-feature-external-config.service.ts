import { Injectable } from '@angular/core';
import { catchError, from, map, Observable, switchMap, throwError } from 'rxjs';
import { SupabaseService } from './supabase.service';
import { PortalFeatureExternalConfig, PortalFeature } from '../types';

@Injectable({
  providedIn: 'root',
})
export class PortalFeatureExternalConfigService {
  constructor(private supabaseService: SupabaseService) {}

  /**
   * Get configuration for a specific portal feature
   */
  getFeatureExternalConfig(featureId: PortalFeature): Observable<PortalFeatureExternalConfig | null> {
    return from(
      this.supabaseService.client
        .from('core_portal_feature_external_config')
        .select('*')
        .eq('portal_feature_id', featureId)
        .single(),
    ).pipe(
      map((response) => {
        if (response.error) {
          if (response.error.code === 'PGRST116') {
            // No rows found
            return null;
          }
          console.error('Error fetching feature config:', response.error);
          throw response.error;
        }
        return response.data;
      }),
      catchError((error) => {
        console.error('Error fetching feature config:', error);
        return throwError(() => error);
      }),
    );
  }

  /**
   * Get all portal feature configurations
   */
  getAllFeatureExternalConfigs(): Observable<PortalFeatureExternalConfig[]> {
    return from(
      this.supabaseService.client
        .from('core_portal_feature_external_config')
        .select('*')
        .order('portal_feature_id', { ascending: true }),
    ).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error fetching all feature configs:', response.error);
          throw response.error;
        }
        return response.data || [];
      }),
      catchError((error) => {
        console.error('Error fetching all feature configs:', error);
        return throwError(() => error);
      }),
    );
  }

  /**
   * Create or update configuration for a portal feature
   */
  upsertFeatureExternalConfig(
    featureId: PortalFeature,
    config: any,
  ): Observable<PortalFeatureExternalConfig> {
    const configData = {
      portal_feature_id: featureId,
      config: config,
    };

    return from(
      this.supabaseService.client
        .from('core_portal_feature_external_config')
        .upsert([configData], {
          onConflict: 'portal_feature_id',
          ignoreDuplicates: false,
        })
        .select()
        .single(),
    ).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error upserting feature config:', response.error);
          throw response.error;
        }
        return response.data;
      }),
      catchError((error) => {
        console.error('Error upserting feature config:', error);
        return throwError(() => error);
      }),
    );
  }

  /**
   * Update configuration for a portal feature
   */
  updateFeatureExternalConfig(
    featureId: PortalFeature,
    config: any,
  ): Observable<PortalFeatureExternalConfig> {
    return from(
      this.supabaseService.client
        .from('core_portal_feature_external_config')
        .update({ config: config })
        .eq('portal_feature_id', featureId)
        .select()
        .single(),
    ).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error updating feature config:', response.error);
          throw response.error;
        }
        return response.data;
      }),
      catchError((error) => {
        console.error('Error updating feature config:', error);
        return throwError(() => error);
      }),
    );
  }

  /**
   * Delete configuration for a portal feature
   */
  deleteFeatureExternalConfig(featureId: PortalFeature): Observable<void> {
    return from(
      this.supabaseService.client
        .from('core_portal_feature_external_config')
        .delete()
        .eq('portal_feature_id', featureId),
    ).pipe(
      map((response) => {
        if (response.error) {
          console.error('Error deleting feature external config:', response.error);
          throw response.error;
        }
      }),
      catchError((error) => {
        console.error('Error deleting feature external config:', error);
        return throwError(() => error);
      }),
    );
  }

  /**
   * Get configuration value for a specific key within a feature
   */
  getExternalConfigValue<T = any>(
    featureId: PortalFeature,
    key: string,
    defaultValue?: T,
  ): Observable<T> {
    return this.getFeatureExternalConfig(featureId).pipe(
      map((config) => {
        if (!config || !config.config) {
          return defaultValue as T;
        }
        return config.config[key] !== undefined
          ? config.config[key]
          : (defaultValue as T);
      }),
    );
  }

  /**
   * Set configuration value for a specific key within a feature
   */
  setExternalConfigValue(
    featureId: PortalFeature,
    key: string,
    value: any,
  ): Observable<PortalFeatureExternalConfig> {
    return this.getFeatureExternalConfig(featureId).pipe(
      map((existingConfig) => {
        const currentConfig = existingConfig?.config || {};
        return {
          ...currentConfig,
          [key]: value,
        };
      }),
      switchMap((newConfig) => this.upsertFeatureExternalConfig(featureId, newConfig)),
    );
  }
}
