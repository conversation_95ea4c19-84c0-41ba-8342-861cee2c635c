import { Injectable } from '@angular/core';
import type {
  FeatureConfigType,
  PortalConfig,
  PortalFeatureDefinition,
  SupabaseConfig,
} from '../types';
import { PortalFeature } from '../types';
import { portalConfig } from '../../portal.config';

@Injectable({
  providedIn: 'root',
})
export class ConfigService {
  private readonly config: PortalConfig = portalConfig;

  constructor() {}

  get supabase(): SupabaseConfig {
    return this.config.supabase;
  }

  get navigation(): PortalFeatureDefinition[] {
    return this.config.features.filter((feature) => feature.enabled !== false);
  }

  get regularNavigation(): PortalFeatureDefinition[] {
    return this.config.features.filter(
      (feature) => feature.enabled !== false && !feature.isSetting,
    );
  }

  get topLevelNavigation(): PortalFeatureDefinition[] {
    return this.config.features.filter(
      (feature) =>
        feature.enabled !== false &&
        !feature.isSetting &&
        !feature.parentFeature,
    );
  }

  get settingsNavigation(): PortalFeatureDefinition[] {
    return this.config.features.filter(
      (feature) => feature.enabled !== false && feature.isSetting,
    );
  }

  get hasSettings(): boolean {
    return this.settingsNavigation.length > 0;
  }

  get n8nBaseUrl(): string {
    return this.config.n8nBaseUrl;
  }

  getChildFeatures(parentFeatureId: PortalFeature): PortalFeatureDefinition[] {
    return this.config.features.filter(
      (feature) =>
        feature.enabled !== false && feature.parentFeature === parentFeatureId,
    );
  }

  hasChildFeatures(featureId: PortalFeature): boolean {
    return this.getChildFeatures(featureId).length > 0;
  }

  getParentFeature(
    childFeatureId: PortalFeature,
  ): PortalFeatureDefinition | null {
    const childFeature = this.config.features.find(
      (f) => f.id === childFeatureId,
    );
    if (!childFeature?.parentFeature) return null;

    return (
      this.config.features.find((f) => f.id === childFeature.parentFeature) ||
      null
    );
  }

  getCustomerFeatureConfig<T extends PortalFeature>(
    featureId: T,
  ): Partial<FeatureConfigType<T>> | null {
    const featureConfigs = this.config.featureConfigs || {};
    return (featureConfigs[featureId] as Partial<FeatureConfigType<T>>) || null;
  }
}
