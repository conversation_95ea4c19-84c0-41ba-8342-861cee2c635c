<div class="login-container">
  <div class="login-card">
    <!-- Logo -->
    <div class="logo-section">
      <img [src]="logoUrl" alt="Chainmatic Portal" class="logo">
    </div>

    <!-- Error Message -->
    <div *ngIf="error" class="error-container">
      <div class="error-icon">
        <i class="pi pi-exclamation-triangle"></i>
      </div>
      <span class="error-text">{{ error }}</span>
    </div>

    <!-- Login Form -->
    <form (ngSubmit)="onSubmit()" (keydown)="onKeyDown($event)" [formGroup]="loginForm" class="login-form">
      <!-- Email Field -->
      <div class="field">
        <input
          autocomplete="email"
          class="field-input"
          formControlName="email"
          id="email"
          pInputText
          placeholder="Email address">
      </div>

      <!-- Password Field -->
      <div class="field">
        <input
          autocomplete="current-password"
          class="field-input"
          formControlName="password"
          pInputText
          placeholder="Password"
          type="password">
      </div>

      <!-- Remember Me -->
      <div class="remember-section">
        <p-checkbox
          [binary]="true"
          formControlName="rememberMe"
          inputId="remember-me">
        </p-checkbox>
        <label class="checkbox-label" for="remember-me">Remember me</label>
      </div>

      <!-- Submit Button -->
      <div class="button-container">
        <p-button
          [disabled]="loginForm.invalid || loading"
          [loading]="loading"
          [label]="loading ? 'Signing in...' : 'Sign in'"
          styleClass="submit-btn"
          type="submit">
        </p-button>
      </div>
    </form>
  </div>
</div>
