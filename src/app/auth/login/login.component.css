.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.login-card {
  width: 100%;
  max-width: 380px;
  padding: 2rem;
  border-radius: 1rem;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(254, 236, 249, 0.3) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Logo */
.logo-section {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
}

.logo {
  height: 50px;
  width: auto;
  max-width: 220px;
  object-fit: contain;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

/* Error Message */
.error-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.875rem 1rem;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.05) 100%);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: 0.75rem;
  backdrop-filter: blur(10px);
  animation: slideIn 0.3s ease-out;
}

.error-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: rgba(239, 68, 68, 0.15);
  color: #dc2626;
  font-size: 0.75rem;
  flex-shrink: 0;
}

.error-text {
  color: #dc2626;
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.4;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Form */
.login-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.field {
  display: flex;
  flex-direction: column;
}

.field-input {
  width: 100% !important;
  padding: 0.75rem !important;
  border-radius: 0.5rem !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  font-size: 0.875rem !important;
  transition: all 0.2s ease !important;
  background: rgba(255, 255, 255, 0.8) !important;
  height: 44px !important;
}

.field-input:focus {
  border-color: #5521be !important;
  box-shadow: 0 0 0 2px rgba(85, 33, 190, 0.1) !important;
  background: rgba(255, 255, 255, 0.95) !important;
  outline: none !important;
}

/* Button Container */
.button-container {
  display: flex;
  justify-content: center;
  margin-top: 0.25rem;
}

/* Remember Me */
.remember-section {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: -0.25rem 0 0.25rem 0;
}

.remember-checkbox {
  font-size: 0.875rem;
}

.checkbox-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #64748b;
  cursor: pointer;
  user-select: none;
  transition: color 0.2s ease;
  line-height: 1;
  display: flex;
  align-items: center;
  margin-top: 0.4rem;
}

.checkbox-label:hover {
  color: #5521be;
}

/* Submit Button */
.submit-btn {
  width: 200px !important;
  padding: 0.75rem !important;
  font-weight: 600 !important;
  border-radius: 0.5rem !important;
  background: linear-gradient(135deg, #5521be 0%, #e036af 100%) !important;
  border: none !important;
  font-size: 0.9375rem !important;
  height: 44px !important;
}

.submit-btn:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(85, 33, 190, 0.3) !important;
}

/* Responsive */
@media (max-width: 480px) {
  .login-container {
    padding: 1rem;
  }

  .login-card {
    padding: 1.5rem;
  }

  .logo {
    height: 45px;
  }
}

/* PrimeNG Overrides */
:host ::ng-deep .p-message {
  margin: 0;
  border-radius: 0.75rem;
}

/* Checkbox styling for PrimeNG 19 with CSS Custom Properties */
:host ::ng-deep .p-checkbox {
  --p-checkbox-width: 18px;
  --p-checkbox-height: 18px;
  --p-checkbox-border-radius: 4px;
  --p-checkbox-border-width: 1px;
  --p-checkbox-border-color: rgba(255, 255, 255, 0.5);
  --p-checkbox-background: rgba(255, 255, 255, 0.9);
  --p-checkbox-hover-border-color: #5521be;
  --p-checkbox-hover-background: rgba(255, 255, 255, 1);
  --p-checkbox-checked-background: #5521be;
  --p-checkbox-checked-border-color: #5521be;
  --p-checkbox-checked-hover-background: #4a1a9e;
  --p-checkbox-checked-hover-border-color: #4a1a9e;
  --p-checkbox-icon-checked-color: white;
  --p-checkbox-icon-checked-hover-color: white;

  width: 18px !important;
  height: 18px !important;
}

:host ::ng-deep .p-checkbox .p-checkbox-box {
  transition: all 0.2s ease !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}
