.password-setup-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.password-setup-card {
  width: 100%;
  max-width: 420px;
  padding: 2rem;
  border-radius: 1rem;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(254, 236, 249, 0.3) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Logo */
.logo-section {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
}

.logo {
  height: 50px;
  width: auto;
  max-width: 220px;
  object-fit: contain;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

/* Header */
.header-section {
  text-align: center;
  margin-bottom: 2rem;
}

.title {
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #1f2937;
}

.subtitle {
  font-size: 0.9rem;
  color: #6b7280;
  margin: 0;
  line-height: 1.4;
}

/* Error Message */
.error-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 0.5rem;
  margin-bottom: 1.5rem;
}

.error-icon {
  color: #ef4444;
  font-size: 1rem;
}

.error-text {
  color: #b91c1c;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Form */
.password-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.field-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #1f2937;
}

.field-input {
  width: 100%;
  height: 2.75rem;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  background: #ffffff;
  font-size: 1rem;
  color: #1f2937;
  transition: all 0.2s ease;
}

.field-input:focus {
  outline: none;
  border-color: #5521be;
  box-shadow: 0 0 0 2px rgba(85, 33, 190, 0.2);
}

.field-input::placeholder {
  color: #6b7280;
}

.field-error {
  font-size: 0.75rem;
  color: #ef4444;
  margin-top: 0.25rem;
}

/* Buttons */
.button-container {
  margin-top: 1rem;
}

.submit-btn {
  height: 2.75rem;
}

/* Responsive */
@media (max-width: 480px) {
  .password-setup-container {
    padding: 1rem;
  }

  .password-setup-card {
    padding: 1.5rem;
  }

  .title {
    font-size: 1.25rem;
  }

  .subtitle {
    font-size: 0.85rem;
  }
}
