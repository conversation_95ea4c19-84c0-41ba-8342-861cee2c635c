#!/bin/sh

eval $(npx tsx config-helper.ts)

CUSTOMER_REPO_SSH="**************:chainmatic/customers/$CUSTOMER_ID.git"
BUILD_DIR="dist/platform/browser"
TMP_DIR=".coolify-deploy-tmp"

if [ -f .env ]; then
    export $(cat .env | xargs)
fi

start_time=$(date +%s)

echo "🔨 Building Angular project..."
ng build --configuration production --base-href=/

echo "📦 Preparing temporary deploy folder..."
rm -rf $TMP_DIR
mkdir -p $TMP_DIR
cp -r $BUILD_DIR/* $TMP_DIR

cd $TMP_DIR

echo "🚀 Initializing Git repo for deploy..."
git init
git remote add origin $CUSTOMER_REPO_SSH
git checkout -b main

git add .
git commit -m "Deploy $(date '+%Y-%m-%d %H:%M:%S')"
git push --force origin main

cd ..
rm -rf $TMP_DIR

echo "🔄 Initiating Coolify auto-deployment..."
curl -X POST "$N8N_BASE_URL/webhook/portal/deployment" \
  -H "Authorization: $COOLIFY_DEPLOYMENT_API_KEY"
echo ""

end_time=$(date +%s)
elapsed_time=$((end_time - start_time))
minutes=$((elapsed_time / 60))
seconds=$((elapsed_time % 60))

echo "✅ Angular build deployed to $CUSTOMER_REPO_SSH in $minutes min $seconds sec."
